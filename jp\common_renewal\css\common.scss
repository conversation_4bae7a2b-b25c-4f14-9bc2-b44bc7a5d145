$DESIGN_WIDTH_FHD: 1920;
$DESIGN_WIDTH_PC: 1366;
$DESIGN_WIDTH_SP: 375;
$CONTAINER_WIDTH: 1196;
$WIDTH_SP: 767px;
$WIDTH_SM: 540px;
$WIDTH_FHD: 1920px;
$WIDTH_PC: 1366;
$WIDTH_LAPTOP: 1260px;
$WIDTH_TABLET: 1023px;
$WIDTH_INNER: 119.6rem;
$FZ_PC: 16;
$FZ_SP: 14;
$FONT_BASE: 10;
$OPACITY_HOVER: 0.75;
$TRANSITION: 0.25s;
$COLOR_OKK_BASE: #413e3e;
$COLOR_OKK_RED: #d98794;
$COLOR_OKK_RED_LIGHT: #faf2f4;
$COLOR_OKK_BLUE: #90b5d1;
$COLOR_OKK_BLUE_LIGHT: #f2f5f7;
$COLOR_OKK_GRAY_LIGHT: #f8f8f8;
$COLOR_OKK_GRAY_LIGHT2: #898989;
$COLOR_OKK_BORDER: #c3c3c3;
$COLOR_SHADOW: rgba(#000, 0.16);
$SHADOW_BASE: 0 0 0.6rem $COLOR_SHADOW;
$FONT_GOTHIC: "Noto Sans JP",
  "ヒラギノ角ゴ ProN",
  "ヒラギノ角ゴ Pro",
  "ヒラギノ角ゴ StdN",
  "Hiragino Kaku Gothic ProN",
  "YuGothic",
  "游ゴシック",
  "Yu Gothic",
  "游ゴシック Medium",
  "Yu Gothic Medium",
  "ヒラギノ⾓ゴ Pro",
  "Hiragino Kaku Gothic Pro",
  "MS PGothic",
  sans-serif;
$FONT_MINCHO: "Noto Serif JP",
  serif;

// > 1366
@mixin designUp() {
  @media screen and (min-width: ($WIDTH_PC+1)) {
    @content;
  }
}

// 1366 - 768
@mixin laptop_lg() {
  @media screen and (min-width: ($WIDTH_SP+1)) and (max-width: $WIDTH_PC) {
    @content;
  }
}

// 1260 - 768
@mixin laptop() {
  @media screen and (min-width: ($WIDTH_SP+1)) and (max-width: $WIDTH_LAPTOP) {
    @content;
  }
}

// 1024 - 768
@mixin tablet() {
  @media screen and (min-width: ($WIDTH_SP+1)) and (max-width: $WIDTH_TABLET) {
    @content;
  }
}

// > 1024
@mixin tabletUp() {
  @media screen and (min-width: $WIDTH_TABLET) {
    @content;
  }
}

// >= 768
@mixin pc() {
  @media screen and (min-width: ($WIDTH_SP+1)) {
    @content;
  }
}

// < 768
@mixin sp() {
  @media screen and (max-width: $WIDTH_SP) {
    @content;
  }
}

@mixin sm() {
  @media screen and (max-width: $WIDTH_SM) {
    @content;
  }
}

@mixin sp_lg() {

  @media screen and (max-width: $WIDTH_SP) and (min-width: 480px),
  screen and (max-width: $WIDTH_SP) and (orientation: landscape) {
    @content;
  }
}

@mixin hover() {
  @media (hover: hover) {
    @content;
  }
}

@include pc() {
  .sp {
    display: none !important;
  }
}

@include sp() {
  .pc {
    display: none !important;
  }
}

@function vw($design_width_psd) {
  $vw: calc($design-width-psd / $DESIGN_WIDTH_SP) * 100;
  @return #{$vw}vw;
}

@function rem($design_px) {
  $rem: calc($design_px / 10);
  @return $rem+rem;
}

@mixin fz($FONT_SIZE, $LINE_HEIGHT: null) {
  font-size: calc($FONT_SIZE / 10) + rem;

  @if $LINE_HEIGHT !=null {
    line-height: calc($LINE_HEIGHT / $FONT_SIZE);
  }
}

// Margin, Padding
@for $i from 0 through 20 {
  .okk-mt#{$i * 5} {
    margin-top: 0.5rem * $i !important;
  }

  .okk-mr#{$i * 5} {
    margin-right: 0.5rem * $i !important;
  }

  .okk-mb#{$i * 5} {
    margin-bottom: 0.5rem * $i !important;
  }

  .okk-ml#{$i * 5} {
    margin-left: 0.5rem * $i !important;
  }

  .okk-pt#{$i * 5} {
    padding-top: 0.5rem * $i !important;
  }

  .okk-pr#{$i * 5} {
    padding-right: 0.5rem * $i !important;
  }

  .okk-pb#{$i * 5} {
    padding-bottom: 0.5rem * $i !important;
  }

  .okk-pl#{$i * 5} {
    padding-left: 0.5rem * $i !important;
  }
}

@include pc {
  @for $i from 0 through 20 {
    .okk-pc-mt#{$i * 5} {
      margin-top: 0.5rem * $i !important;
    }

    .okk-pc-mr#{$i * 5} {
      margin-right: 0.5rem * $i !important;
    }

    .okk-pc-mb#{$i * 5} {
      margin-bottom: 0.5rem * $i !important;
    }

    .okk-pc-ml#{$i * 5} {
      margin-left: 0.5rem * $i !important;
    }

    .okk-pc-pt#{$i * 5} {
      padding-top: 0.5rem * $i !important;
    }

    .okk-pc-pr#{$i * 5} {
      padding-right: 0.5rem * $i !important;
    }

    .okk-pc-pb#{$i * 5} {
      padding-bottom: 0.5rem * $i !important;
    }

    .okk-pc-pl#{$i * 5} {
      padding-left: 0.5rem * $i !important;
    }
  }
}

@include sp {
  @for $i from 0 through 20 {
    .okk-sp-mt#{$i * 5} {
      margin-top: 0.5rem * $i !important;
    }

    .okk-sp-mr#{$i * 5} {
      margin-right: 0.5rem * $i !important;
    }

    .okk-sp-mb#{$i * 5} {
      margin-bottom: 0.5rem * $i !important;
    }

    .okk-sp-ml#{$i * 5} {
      margin-left: 0.5rem * $i !important;
    }

    .okk-sp-pt#{$i * 5} {
      padding-top: 0.5rem * $i !important;
    }

    .okk-sp-pr#{$i * 5} {
      padding-right: 0.5rem * $i !important;
    }

    .okk-sp-pb#{$i * 5} {
      padding-bottom: 0.5rem * $i !important;
    }

    .okk-sp-pl#{$i * 5} {
      padding-left: 0.5rem * $i !important;
    }
  }
}

@keyframes fadezoom {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.1);
  }
}

@keyframes animate-svg-stroke {
  0% {
    stroke-dashoffset: 159.07963267948966px;
    stroke-dasharray: 159.07963267948966px;
  }

  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 159.07963267948966px;
  }
}

@keyframes scrollAboutDown {
  0% {
    background-position-y: 0;
  }

  to {
    background-position-y: rem(1253);
  }
}

@keyframes scrollAboutUp {
  0% {
    background-position-y: 0;
  }

  to {
    background-position-y: rem(-1253);
  }
}

@keyframes scrollAboutRight {
  0% {
    background-position-x: 0;
  }

  to {
    background-position-x: rem(687);
  }
}

@keyframes scrollAboutLeft {
  0% {
    background-position-x: 0;
  }

  to {
    background-position-x: rem(-687);
  }
}

a {
  color: inherit;
  overflow: unset;
}

sup {
  vertical-align: super;
  font-size: smaller;
}

.okk-body {
  background: #fff;
  color: $COLOR_OKK_BASE;
  @include fz(16, 24);
  font-weight: 400;
  letter-spacing: 0;

  #wrapper {
    padding-top: rem(141);

    @include sp {
      padding-top: rem(112) !important;
    }
  }

  @include pc {
    a[href^="tel"] {
      pointer-events: none;
      text-decoration: none;
      opacity: 1 !important;
      cursor: default !important;
    }
  }

  @include sp {
    font-size: rem(14);
  }
}

.body-no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.okk-home {
  font-family: $FONT_GOTHIC;

  #wrapper {
    @include pc {
      padding-top: 0 !important;
    }
  }
}

.okk-main {
  background: #fff !important;
}

.okk-img-fit {
  overflow: hidden;

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.okk-red {
  color: $COLOR_OKK_RED;
}

.inline-block {
  display: inline-block;
}

.overflow-hidden {
  overflow: hidden;
}

.okk-bg-gray {
  background: $COLOR_OKK_BLUE_LIGHT;
}

.okk-bg-rose {
  background: $COLOR_OKK_RED_LIGHT;
}

.hover {
  transition: $TRANSITION;

  @include pc {
    &:hover {
      opacity: $OPACITY_HOVER;
    }
  }
}

.okk-inner {
  max-width: $WIDTH_INNER;
  margin: 0 auto;
  padding: 0 rem(20);
}

.okk-btn-wrap {
  margin-top: rem(40);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: rem(20);

  @include sp {
    margin-top: rem(30);
    gap: rem(10);
  }
}

.okk-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid $COLOR_OKK_BASE;
  @include fz(16, 24);
  padding: rem(21) rem(45);
  text-align: center;
  position: relative;
  font-weight: 700;
  border-radius: rem(70);
  width: rem(400);
  max-width: 100%;
  background: #fff;
  @extend .hover;

  &::after {
    position: absolute;
    top: 50%;
    right: rem(28);
    transform: translateY(-50%);
    content: "";
    z-index: 1;
    width: rem(15);
    height: rem(11.7272727273);
    background: url("../img/common/ico_arrow_right.png") no-repeat right center / 100% 100%;
  }

  &--primary {
    background: $COLOR_OKK_RED;
    border-color: $COLOR_OKK_RED;
    color: #fff;

    &::after {
      background-image: url("../img/common/ico_arrow_right_white.png");
    }
  }

  &--tel {
    &::after {
      content: none;
    }
  }

  &--sm {
    padding-top: rem(16);
    padding-bottom: rem(16);
  }

  @include sp {
    padding: rem(16) rem(40);
    border-radius: rem(60);
    width: rem(335);
    max-width: 100%;

    &::after {
      right: rem(26);
      width: rem(13);
      height: rem(10.1636364);
    }

    &--sm {
      padding-top: rem(13);
      padding-bottom: rem(13);
      @include fz(14, 20);
    }
  }
}

.mfp-zoom {
  cursor: default;

  &.mfp-bg {
    opacity: 0;
    background: #000;
    cursor: default;
    transition: 0.25s ease-out;

    &.mfp-ready {
      opacity: 0.8;
    }

    &.mfp-removing {
      opacity: 0;
    }
  }

  &.mfp-wrap {
    .mfp-content {
      opacity: 0;
      transform: scale(0.85);
      transition: 0.25s ease-out;
    }

    &.mfp-ready .mfp-content {
      opacity: 1;
      transform: scale(1);
    }

    &.mfp-removing .mfp-content {
      opacity: 0;
      transform: scale(0.85);
    }
  }
}

.okk-header {
  --currentColor: #413e3e;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  color: var(--currentColor);

  &-top {
    position: relative;
    z-index: 1000;

    &::before {
      position: absolute;
      content: none;
      background: #fff;
      inset: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      opacity: 0;
      transform: translateY(-101%);
      transition: $TRANSITION;

      .okk-home & {
        content: "";
      }
    }

    &__wrap {
      position: relative;
      z-index: 2;
      background: transparent;

      &::after {
        position: absolute;
        content: "";
        z-index: 1;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: #c6c6c6;
      }
    }

    &__inner {
      position: relative;
      z-index: 3;
      max-width: rem(1292);
      padding: 0 rem(30);
      margin: 0 auto;
    }

    &__row {
      align-items: center;
      display: flex;
    }
  }

  &__site {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: rem(793);
    padding-right: rem(20);
    padding-bottom: rem(10);
  }

  &__logo {
    width: rem(119);
    flex-shrink: 0;

    a {
      display: block;
      @extend .hover;
    }
  }

  &__slogan {
    flex-grow: 1;
    padding: rem(5) 0 rem(5) rem(20);
    @include fz(12, 20);

    span {
      display: inline-block;
    }
  }

  &__group-btn {
    flex-grow: 1;
    position: relative;
    display: flex;
    justify-content: space-between;
    padding-top: rem(18);
    padding-bottom: rem(12);
    padding-left: rem(185);
    z-index: 10;
    gap: rem(20);
  }

  .okk-box-lang {
    position: absolute;
    top: rem(18);
    left: 0;
    width: rem(172);
    border: 2px solid $COLOR_OKK_RED;
    background: #fff;
    cursor: pointer;
    border-radius: rem(23);
    color: $COLOR_OKK_RED;

    &__label {
      display: block;
      @include fz(16, 24);
      padding: rem(8) rem(20) rem(10) rem(55);
      position: relative;
      font-weight: 500;

      &::before {
        position: absolute;
        content: "";
        transform: translateY(-50%);
        top: 50%;
        left: rem(21);
        width: rem(25);
        height: rem(25);
        background: url("../img/common/ico_language.png") no-repeat center center / 100% 100%;
      }

      .txt {}
    }

    &__list {
      width: 100%;
      display: none;
      padding: rem(5) rem(10);

      li {
        border-top: 1px dotted $COLOR_OKK_BORDER;
        padding: rem(5) rem(10) rem(5) rem(45);
      }

      a {
        @extend .hover;
      }

      &.is-active {
        opacity: 1;
      }
    }
  }

  &-reserve {
    &__btn {
      a {
        color: #fff;
        background: $COLOR_OKK_RED;
        padding: rem(10) rem(20) rem(12);
        width: rem(187);
        border-radius: rem(50);
        height: rem(50);
        text-align: center;
        font-weight: 700;
        line-height: 1.5;
        display: flex;
        justify-content: center;
        align-items: center;
        @extend .hover;
      }
    }

    &__cancel {
      margin-top: rem(3);
      @include fz(12, 17);
      font-weight: 500;
      text-decoration: underline;
      text-align: center;
      @extend .hover;
    }
  }

  &-hamburger {
    margin-top: rem(13);
    width: rem(40);

    a {
      @extend .hover;
    }

    .hamburger-line {
      margin: 0 auto;
      width: 34px;
      height: 12px;
      position: relative;
      display: block;

      &::before,
      &::after {
        position: absolute;
        content: "";
        background: var(--currentColor);
        width: 100%;
        height: 3px;
        z-index: 1;
        transition: $TRANSITION;
      }

      &::before {
        top: 0;
      }

      &::after {
        bottom: 0;
      }
    }

    .hamburger-txt {
      position: relative;
      @include fz(12, 17);
      font-weight: 500;
      font-weight: 600;
      display: block;
      text-align: center;
      width: 100%;
      height: 17px;
      width: calc(100% + #{rem(20)});
      position: relative;
      margin: rem(6) rem(-10) 0;

      >span {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
      }

      .txt4normal {
        opacity: 1;
      }

      .txt4active {
        opacity: 0;
      }
    }
  }

  &-nav-pc {
    position: relative;
    z-index: 1;
    opacity: 1;
    visibility: visible;

    &__list {
      display: flex;
      justify-content: center;

      >li {
        padding: rem(8) rem(20);

        a {
          line-height: 1.5;
          @extend .hover;
        }

        &.menu-item-hasSub {
          position: relative;

          >a {
            position: relative;
            padding-right: rem(20);

            &::after {
              position: absolute;
              content: "";
              top: 54%;
              transform: translateY(-50%);
              right: 0;
              z-index: 1;
              width: rem(12);
              height: rem(7);
              background: url("../img/common/ico_arrow_menu_down_gray.png") no-repeat center center / 100% 100%;
              transition: all 0.25s ease;

              .okk-home & {
                background-image: url("../img/common/ico_arrow_menu_down.png");
              }
            }
          }

          >ul {
            position: absolute;
            top: 100%;
            left: 50%;
            width: rem(140);
            transform: translateX(-50%);
            background: #fff;
            color: $COLOR_OKK_BASE;
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: all 0.5s ease;
            text-align: left;
            z-index: 1;

            li {
              padding-left: rem(15);

              &:nth-child(n + 2) {
                border-top: 1px solid #c5c5c5;
              }

              a {
                padding: rem(10) 0;
                display: block;
                @include fz(14, 20);
              }
            }
          }

          &:hover {
            >a {
              &::after {
                transform: translateY(-50%) rotate(180deg);
              }
            }

            >ul {
              max-height: 75rem;
              opacity: 1;
              transition: all 0.5s ease;
            }
          }
        }
      }
    }
  }

  &-main {
    position: fixed;
    width: 100%;
    max-height: 100dvh;
    max-height: calc(var(--vh, 1vh) * 100);
    left: 0;
    top: 0;
    padding-top: rem(100);
    z-index: 999;
    color: $COLOR_OKK_BASE;
    opacity: 0;
    visibility: hidden;
    transition: $TRANSITION;
    overflow-y: auto;

    &__inner {
      padding: rem(40) rem(30) rem(60);
      background: #fff;
      border-radius: 0 0 rem(25) rem(25);
    }

    &__row {
      display: flex;
      width: 100%;
      max-width: rem(1155);
      margin: 0 auto;
    }
  }

  &-nav {
    flex-grow: 1;
    display: flex;
    flex-wrap: wrap;
    max-width: calc(100% - #{rem(292)});

    &__col {
      width: 33.333%;
      max-width: rem(287);
    }

    dl {
      @include pc {
        width: rem(230);
        max-width: 94%;
      }

      &:nth-child(n + 2) {
        margin-top: rem(30);
      }

      dt {
        display: flex;
        align-items: center;
        padding-bottom: rem(12);
        border-bottom: 1px solid #c6c6c6;

        img {
          width: rem(25);
          flex-shrink: 0;
        }

        .txt {
          flex-grow: 1;
          padding-left: rem(5);
          @include fz(20, 29);
          font-weight: 700;
          color: $COLOR_OKK_RED;
        }
      }

      dd {}

      @include pc {
        dt {
          pointer-events: none;
        }

        dd {
          display: block !important;
        }
      }
    }

    &__list {
      >li {
        padding-top: rem(12);

        a {
          line-height: 1.5;
          font-weight: 500;
          @extend .hover;
        }

        >ul {
          padding-top: rem(4);

          li {
            margin-top: rem(8);
            line-height: 1;

            a {
              @include fz(14, 20);
              position: relative;
              padding-left: rem(13);

              &::before {
                position: absolute;
                content: "";
                left: 0;
                top: rem(10);
                background: $COLOR_OKK_RED;
                width: rem(9);
                height: 1px;
              }
            }
          }
        }
      }
    }
  }

  &-corporate {
    padding-top: rem(23);
    width: rem(292);
    flex-shrink: 0;

    &__list {
      li {
        margin-bottom: rem(14);
      }
    }

    &__inner {
      display: flex;
      align-items: center;
      gap: rem(9);
    }

    &__img {
      aspect-ratio: 1/1;
      border-radius: rem(6);
      @extend .okk-img-fit;
      width: rem(75);
      flex-shrink: 0;
    }

    &__desc {
      flex-grow: 1;
    }

    &__copy {
      @include fz(12, 17);
      font-weight: 400;
    }

    &__ttl {
      @include fz(14, 20);
      font-weight: 700;
      margin-top: rem(4);
    }

    &__link {
      padding: rem(7);
      display: block;
      background: #fff;
      border-radius: rem(12);
      box-shadow: $SHADOW_BASE;
      @extend .hover;
      position: relative;

      &::after {
        position: absolute;
        top: 50%;
        right: rem(14);
        transform: translateY(-50%);
        z-index: 1;
        content: "";
        background: no-repeat center center / 100% 100%;
        width: rem(15);
        height: rem(15);
      }

      &[target="_blank"] {
        &::after {
          background-image: url("../img/common/ico_extend.png");
        }
      }
    }
  }

  &-sns {
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(10);
    border-radius: rem(12);

    &__headline {
      text-align: center;
      @include fz(14, 20);
      font-weight: 700;
      color: $COLOR_OKK_GRAY_LIGHT2;
      margin-bottom: rem(7);
    }

    &__list {
      padding-bottom: rem(2);
      display: flex;
      justify-content: center;
      gap: rem(18);

      li {
        width: rem(35);

        a {
          display: block;
          @extend .hover;
        }
      }
    }
  }

  &.is-fixed {
    .okk-header {
      &-top {
        &::before {
          opacity: 1;
          transform: translateY(0);
        }

        &__wrap {
          border-bottom-color: #c6c6c6;
        }
      }

      &-nav-pc {
        &__list>li.menu-item-hasSub>a::after {
          .okk-home & {
            background-image: url("../img/common/ico_arrow_menu_down_gray.png");
          }
        }
      }
    }
  }

  &.is-open {
    .okk-header {
      &-top__wrap {
        background: #fff;
        border-bottom-color: #c6c6c6;
      }

      &-nav-pc {
        opacity: 0;
        visibility: hidden;
      }

      &-hamburger {
        .hamburger-line {
          &::before {
            transform: translateY(4px) rotateZ(28deg);
          }

          &::after {
            transform: translateY(-4px) rotateZ(-28deg);
          }
        }

        .hamburger-txt {
          .txt4normal {
            opacity: 0;
          }

          .txt4active {
            opacity: 1;
          }
        }
      }

      &-main {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  .okk-home & {
    @include pc {
      --currentColor: #fff;

      &.is-fixed,
      &.is-open {
        --currentColor: #413e3e;
      }
    }
  }

  .okk-body:not(.okk-home) & {
    @include pc {
      &-top {
        background: #fff;
      }
    }
  }

  @include sp {
    &-top {
      background: #fff;

      &::before {
        content: none;
      }

      &__wrap {
        border-bottom: none;
      }

      &__inner {
        padding: 0 rem(20);
      }

      &__row {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &__site {
      justify-content: flex-start;
      align-items: center;
      max-width: 100%;
      padding-right: rem(40);
      padding-bottom: 0;
    }

    &__logo {
      padding: rem(11) 0;
      width: rem(89);
    }

    &__slogan {
      padding: rem(0) 0 rem(0) rem(10);
      @include fz(12, 17);

      @include sm {
        padding-right: rem(8);
        @include fz(10, 15);
      }
    }

    &__group-btn {
      position: static;
      flex-direction: row-reverse;
      padding: 0;
      gap: 0;
      margin: 0 rem(-20);
      width: calc(100% + #{rem(40)});
    }

    .okk-box-lang {
      position: fixed;
      z-index: 1001;
      top: rem(13);
      width: rem(110);
      left: auto;
      right: rem(8);
      border: none;
      border-radius: 0;
      color: $COLOR_OKK_RED;
      text-align: center;
      background: transparent;

      &__label {
        width: rem(43);
        margin-left: auto;
        display: block;
        @include fz(12, 17);
        padding: rem(21) 0 0;

        &::before {
          left: 50%;
          top: 0;
          transform: translateX(-50%);
          width: rem(21);
          height: rem(21);
        }
      }

      &__list {
        border: 1px solid $COLOR_OKK_RED;
        box-shadow: $SHADOW_BASE;
        margin-top: rem(7);
        padding: rem(10);
        border-radius: rem(10);
        overflow: hidden;
        background: #fff;

        li {
          padding: rem(7) 0;
          font-size: rem(12);
          font-weight: 600;
          border-top-width: 1px;

          &:first-child {
            border-top: none;
          }
        }
      }

      @include sm {
        width: rem(72);
        top: rem(14);

        &__label {
          @include fz(10, 15);
        }

        &__list {
          border-width: 0.5px;
          margin-top: rem(5);
          padding: rem(5);
          border-radius: rem(6);

          li {
            padding: rem(7) 0;
            font-size: rem(10);
            border-top-width: 0.5px;
          }
        }
      }
    }

    &-reserve {
      width: 50%;

      &__btn {
        a {
          padding: rem(6) rem(21.5) rem(6) rem(20);
          width: 100%;
          height: rem(52);
          border-radius: 0;
          line-height: 1.4285714286;
          position: relative;

          &::after {
            position: absolute;
            content: "";
            z-index: 1;
            top: 54%;
            right: rem(13);
            transform: translateY(-50%);
            height: rem(11);
            width: rem(6.05);
            background: url("../img/common/ico_arrow_menu_right.png") no-repeat center center / 100% 100%;
          }
        }
      }

      &__cancel {
        @include fz(14, 20);
        background: $COLOR_OKK_GRAY_LIGHT;
        padding: rem(13) rem(10);
        text-align: center;
        border-radius: rem(10);
        width: 100%;
        max-width: rem(335);
        margin: 0 auto;
      }
    }

    &-hamburger {
      border-top: 1px solid $COLOR_OKK_BORDER;
      padding-top: rem(4);
      margin-top: 0;
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;

      a {
        width: rem(32);
      }

      .hamburger-line {
        width: rem(31);
        height: rem(10);

        &::before,
        &::after {
          height: 2px;
        }
      }

      .hamburger-txt {
        @include fz(10, 15);
        height: 15px;
        margin: rem(4) rem(-10) 0;
      }
    }

    &-nav-pc {
      border-top: none;

      &__list {
        padding: rem(10) 0;
        flex-wrap: wrap;
        justify-content: flex-start;

        >li {
          width: 50%;
          font-size: rem(14);
        }
      }
    }

    &-main {
      padding-top: rem(112);

      &__inner {
        padding: rem(18) rem(20) rem(30);
        border-radius: 0;
      }

      &__row {
        display: block;
        width: 100%;
        max-width: rem(335);
        margin: 0 auto;
      }
    }

    &-nav {
      padding-right: 0;
      display: block;
      width: 100%;
      max-width: 100%;

      &__col {
        width: 100%;
        max-width: 100%;
      }

      dl {
        &:nth-child(n + 2) {
          margin-top: 0;
        }

        dt {
          padding-top: rem(20);
          padding-bottom: rem(18);
          position: relative;

          &::before,
          &::after {
            position: absolute;
            content: "";
            z-index: 1;
            margin-top: rem(2);
            width: rem(18);
            height: 1px;
            background: $COLOR_OKK_BASE;
            right: rem(10);
            top: 50%;
            transform: translateY(-50%);
          }

          &::after {
            transform: translateY(-50%) rotate(90deg);
            transition: $TRANSITION;
          }

          img {
            width: rem(25);
          }

          .txt {
            @include fz(18, 26);
          }

          &.is-active {
            &::after {
              opacity: 0;
            }
          }
        }

        dd {
          padding-bottom: rem(20);
        }
      }

      &__list {
        padding-top: rem(4);

        >li {
          padding-top: rem(14);

          >a {
            @include fz(16);
          }

          >ul {
            display: flex;
            flex-wrap: wrap;
            padding-bottom: rem(12);

            li {
              margin-top: rem(10);
              width: 50%;

              a {
                display: inline-block;
                padding-left: rem(20);

                &::before {
                  content: none;
                }
              }
            }
          }
        }
      }
    }

    &-corporate {
      padding-top: rem(40);
      width: 100%;
      max-width: rem(355);

      &__list {
        li {
          margin-bottom: rem(12);
        }
      }

      &__inner {
        gap: rem(10);
      }

      &__img {
        width: rem(76);
      }

      &__ttl {
        @include fz(20, 29);
      }

      &__link {
        border-radius: rem(10);

        &::after {
          right: rem(12);
          width: rem(16);
          height: rem(16);
        }
      }
    }

    &-sns {
      padding: rem(13);
      border-radius: rem(10);

      &__list {
        gap: rem(20);
      }
    }

    &.is-fixed {
      .okk-header {
        &-top {
          &::before {
            opacity: 1;
            transform: translateY(0);
          }
        }

        &-nav-pc {
          border-top-color: #c6c6c6;

          &__list>li.menu-item-hasSub>a::after {
            background-image: url(../img/common/ico_arrow_menu_down_gray.png);
          }
        }
      }
    }

    &.is-open {
      .okk-header {
        &-top__wrap {
          background: #fff;
        }

        &-nav-pc {
          opacity: 0;
          visibility: hidden;
        }

        &-main {
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }
}

.okk-pagetop {
  position: fixed;
  right: rem(30);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: opacity $TRANSITION !important;
  cursor: pointer;
  width: rem(67);
  height: rem(67);
  bottom: rem(30);

  &.is-active {
    visibility: visible;
    opacity: 1;
    @extend .hover;
  }

  &.is-absolute {
    position: absolute;
  }

  @include sp {
    right: rem(20);
    width: rem(56);
    height: rem(56);
  }
}

.okk-footer {
  position: relative;

  &-headline {
    @include fz(26, 37);
    font-weight: 700;
  }

  &__top {
    text-align: center;
    background: $COLOR_OKK_GRAY_LIGHT;
  }

  &-sns {
    padding: rem(40) 0;

    &__list {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: rem(15);

      li {
        width: rem(46);
      }

      a {
        @extend .hover;
      }
    }
  }

  &-cta {
    padding: rem(40) 0;
    border-top: 1px solid $COLOR_OKK_BORDER;

    &__headline {}

    &__btn {
      max-width: rem(400);
      margin: rem(14) auto 0;
      display: flex;
    }
  }

  &-call {
    display: none;
    border-top: 1px solid $COLOR_OKK_BORDER;
    padding: rem(40) 0;

    &__copy {
      margin-top: rem(4);
      @include fz(14, 20);
      font-weight: 400;
    }

    &__list {
      margin-top: rem(20);
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: rem(20);

      li {
        width: rem(138);
        flex-shrink: 0;
      }

      a {
        display: block;
        padding: rem(10) 0;
        background: #fff;
        box-shadow: $SHADOW_BASE;
        border-radius: rem(15);
        @include fz(16, 24);
      }

      .store-name {
        color: $COLOR_OKK_RED;
        font-weight: 700;
      }

      .tel-number {
        display: block;
      }
    }
  }

  &-logo {
    padding: rem(50) 0;
    display: flex;
    justify-content: center;
    align-items: center;

    a {
      width: rem(193);
      @extend .hover;
    }
  }

  &-nav {
    display: flex;
    justify-content: center;
    align-items: center;

    li {
      padding: 0 rem(10);

      &:nth-child(n + 2) {
        position: relative;

        &::before {
          position: absolute;
          content: "";
          top: 50%;
          left: 0;
          width: 1px;
          height: rem(17);
          transform: translateY(-50%);
          background: $COLOR_OKK_BASE;
        }
      }
    }

    a {
      line-height: 1.5;
      @extend .hover;
    }

    @include sp {
      li {
        &:nth-child(1) {
          padding-left: 0;
        }

        &:nth-child(3) {
          padding-right: 0;
        }

        a {
          font-size: 1.2rem;
        }
      }
    }
  }

  &-language {
    padding-top: rem(4);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;

    li {
      margin-top: rem(10);
      line-height: 1;
      padding: 0 rem(10);

      &:nth-child(n + 2) {
        border-left: 1px solid $COLOR_OKK_BASE;
      }
    }

    a {
      display: block;
      @include fz(10, 15);
      @extend .hover;
    }
  }

  &-fc {
    padding: rem(30) 0 rem(40);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: rem(28);

    a {
      width: rem(254);
      max-width: 46%;
      flex-shrink: 0;
      display: block;
      @extend .hover;
    }
  }

  &-copyright {
    border-top: 1px solid $COLOR_OKK_BORDER;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: rem(30) 0 rem(40);
    @include fz(10, 15);

    a {
      @extend .hover;
    }
  }

  @include sp {
    &-headline {
      @include fz(18, 28);
    }

    &-sns {
      padding: rem(32) 0 rem(28);

      &__list {
        gap: rem(22);

        li {
          width: rem(38);
        }
      }
    }

    &-cta {
      padding: rem(30) 0 rem(40);

      &__btn {
        max-width: rem(335);
        margin: rem(20) auto 0;
      }
    }

    &-call {
      padding: rem(30) 0;

      &__copy {
        margin-top: rem(6);
      }

      &__list {
        justify-content: flex-start;
        gap: rem(11);

        li {
          width: rem(162);
          max-width: calc(50% - #{rem(5.5)});
        }

        a {
          border-radius: rem(10);
        }
      }
    }

    &-logo {
      padding: rem(30) 0 rem(26);

      a {
        width: rem(128);
      }
    }

    &-nav {
      li {
        &:nth-child(n + 2) {
          &::before {
            height: rem(16);
          }
        }
      }
    }

    &-language {
      display: block;
      max-width: rem(335);
      margin: rem(26) auto 0;

      li {
        padding: 0;

        &:nth-child(n + 2) {
          margin-top: rem(10);
          border-left: none;
        }
      }

      a {
        @include fz(12, 17);
        text-decoration: underline;
      }
    }

    &-fc {
      padding: rem(26) 0 rem(30);
      gap: rem(11);

      a {
        width: rem(162);
        max-width: calc(50% - #{rem(5.5)});
      }
    }

    &-copyright {
      padding: rem(20) 0;
    }
  }
}

.okk-section-wrap {
  padding: rem(60) 0;

  @include sp {
    padding: rem(50) 0 rem(60);
  }
}

.okk-section-head {
  text-align: center;
  margin-bottom: rem(40);

  @include sp {
    margin-bottom: rem(30);
  }
}

.okk-page-title {
  text-align: center;
  color: $COLOR_OKK_RED;
  @include fz(24, 38);
  font-weight: 700;
  letter-spacing: 0;
  margin-bottom: rem(40);

  &.color-blue {
    color: #90b5d1;
  }

  @include sp {
    @include fz(20, 30);
    margin-bottom: rem(20);
  }
}

.okk-headline {
  .label {
    display: inline-flex;
    background: $COLOR_OKK_BLUE;
    color: #fff;
    @include fz(16, 24);
    font-weight: 700;
    padding: rem(9) rem(20);
    border-radius: rem(40);
    position: relative;
    margin-bottom: rem(18);
    letter-spacing: 0;

    &::after {
      position: absolute;
      content: "";
      z-index: 1;
      width: rem(23);
      height: rem(12);
      background: url("../img/common/ico_triang_ttl.png") no-repeat center center / cover;
      top: calc(100% - #{rem(4)});
      left: 50%;
      transform: translateX(-50%);
      pointer-events: none;
    }
  }

  .txt {
    display: block;
    @include fz(32, 46);
    font-weight: 700;
    letter-spacing: 0;
  }

  &+.okk-copy {
    margin-top: rem(20);
  }

  @include sp {
    .label {
      @include fz(14, 20);
      padding: rem(11) rem(22);
      margin-bottom: rem(18);
    }

    .txt {
      @include fz(26, 37);
    }
  }
}

.okk-sub-title {
  @include fz(24, 35);
  font-weight: 700;
  margin-bottom: rem(20);
  color: $COLOR_OKK_GRAY_LIGHT2;
  text-align: center;

  &.color-base {
    color: $COLOR_OKK_BASE;
  }

  @include sp {
    @include fz(18, 26);
    margin-bottom: rem(16);
  }
}

.okk-heading-lv2 {
  @include fz(32, 46);
  font-weight: 700;
  margin-bottom: rem(10);

  @include sp {
    @include fz(24, 35);
    margin-bottom: rem(8);
  }
}

.okk-heading-lv3 {
  @include fz(24, 36);
  font-weight: 700;
  margin-bottom: rem(30);

  .sm {
    @include fz(18, 30);
  }

  @include sp {
    @include fz(18, 26);
    margin-bottom: rem(16);

    .sm {
      @include fz(14, 26);
    }
  }
}

.okk-heading-lv4 {
  @include fz(18, 26);
  font-weight: 700;
  margin-bottom: rem(10);

  @include sp {
    @include fz(14, 20);
    margin-bottom: rem(8);
  }
}

.okk-copy {
  line-height: 2;
  font-weight: 500;
  letter-spacing: 0;

  @include sp {
    line-height: 1.85714286;
  }

  @include sm {
    text-align: left;
  }
}

.okk-list-dot {
  @include fz(14, 26);
  font-weight: 500;

  li {
    padding-left: rem(21);
    position: relative;

    &::before {
      position: absolute;
      content: "●";
      top: 0;
      left: 0;
    }

    &:nth-child(n + 2) {
      margin-top: rem(2);
    }
  }
}

.okk-note {
  @include fz(12, 18);
  font-weight: 500;

  li+li {
    margin-top: rem(2);
  }
}

.okk-text-link {
  text-decoration: underline;
  text-underline-offset: 2px;

  @include pc {
    &:hover {
      text-decoration: none;
    }
  }
}

.okk-iframe-ytb {
  aspect-ratio: 16/9;

  iframe {
    display: block;
    width: 100%;
    height: 100%;
  }
}

.slider-scrollbar-wrap {
  position: relative;
}

.slider-progress {
  height: rem(66);
  width: calc(100% - #{rem(180)});
  display: block;
  position: relative;

  &::before {
    position: absolute;
    content: "";
    z-index: 1;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: rem(4);
    border-radius: rem(10);
    overflow: hidden;
    background-color: #e2e2e2;

    .okk-bg-gray & {
      background-color: #fff;
    }
  }

  &__bar {
    position: absolute;
    z-index: 2;
    background: $COLOR_OKK_BLUE;
    height: rem(8);
    transition: width 0.3s ease;
    border-radius: rem(10);
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 0;
    color: transparent;
    text-indent: -9999rem;
  }

  @include sp {
    height: rem(42);
    width: calc(100% - #{rem(120)});
  }
}

.slider-scrollbar-list {
  padding-bottom: rem(40);
  margin-right: calc(50% - 50vw) !important;
  position: static;

  .slick-arrow {
    width: rem(66);
    height: rem(66);
    border: 2px solid $COLOR_OKK_BASE;
    background: #fff;
    border-radius: 50%;
    top: auto;
    bottom: 0;
    transform: translate(0, 0);
    @extend .hover;

    &::before {
      content: none;
    }

    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      content: "";
      z-index: 1;
      width: rem(15);
      height: rem(11.7272727273);
      background: no-repeat center center / 100% 100%;
    }

    &.slick-prev {
      left: auto;
      right: calc(#{rem(66)} + #{rem(14)} + #{rem(17)});

      &::after {
        background-image: url("../img/common/ico_arrow_left.png");
        background-position: center left;
      }
    }

    &.slick-next {
      right: rem(17);

      &::after {
        background-image: url("../img/common/ico_arrow_right.png");
        background-position: center right;
      }
    }
  }

  @include sp {
    padding-bottom: rem(30);
    margin-right: rem(-20);

    .slick-arrow {
      width: rem(42);
      height: rem(42);
      border-width: 1px;

      &::after {
        width: rem(13);
        height: rem(10.1636364);
      }

      &.slick-prev {
        right: calc(#{rem(42)} + #{rem(10)});
      }

      &.slick-next {
        right: 0;
      }
    }
  }
}

.okk-t-kv {
  position: relative;

  &__desc {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: rem(38);
    pointer-events: none;

    &::after {
      position: absolute;
      content: "";
      z-index: 1;
      top: 0;
      left: 0;
      right: 0;
      width: 100%;
      height: rem(306);
      background-image: linear-gradient(to top,
          rgba(#000, 0),
          rgba(#323232, 0.73));
      mix-blend-mode: multiply;
    }
  }

  &__title {
    position: relative;
    z-index: 2;
    color: #fff;
    font-family: $FONT_MINCHO;
    @include fz(44, 76);
    font-weight: 600;
    letter-spacing: 0.1em;
    text-align: center;
    text-shadow: 0 0 6px rgba(#000, 0.55);
  }

  &__slogan {
    position: absolute;
    bottom: rem(136);
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    color: #fff;
    background: rgba(#90b5d1, 0.8);
    @include fz(16, 22);
    font-weight: 600;
    letter-spacing: 0;
    text-align: center;
    padding: rem(4) rem(10) rem(7);
  }

  &__slider {
    margin: 0 !important;
  }

  .slide-item {
    width: 100vw;
    aspect-ratio: 1366/894;
    @extend .okk-img-fit;

    @media screen and (min-height: 500px) {
      max-height: 100dvh;
    }
  }

  .slide-animation {
    animation: fadezoom 12s 0s forwards;
  }

  .slick-dots {
    top: 50%;
    right: 50px;
    bottom: auto;
    width: auto;
    transform: translateY(-50%);
    counter-reset: number;

    li {
      display: block;
      position: relative;
      width: 40px;
      height: 40px;
      margin: 16px 0;
      border: 1px solid rgba(255, 255, 255, 0);
      border-radius: 100%;
      transition: 0.3s;

      &:before {
        counter-increment: number;
        content: counter(number, decimal-leading-zero);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: rem(14);
      }

      &.slick-active {
        border: 1px solid rgba(255, 255, 255, 0.4);

        .svg-elem-1 {
          animation: animate-svg-stroke 8.5s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both,
            animate-svg-fill-1 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both;
        }
      }

      button:before {
        content: none;
      }
    }
  }

  .c-indicator__circle {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 50%;
    margin: auto;
    position: absolute;
    transform: translate(-50%, -50%);
    outline: none;

    circle {
      stroke-dasharray: 160;
      stroke-dashoffset: 160;
      fill: transparent;
    }
  }

  @include sp {
    &__desc {
      padding-top: 0;
      padding-bottom: rem(31);

      &::after {
        height: rem(285);
        height: 76vw;
      }
    }

    &__title {
      @include fz(26, 38);
    }

    &__slogan {
      bottom: rem(40);
      @include fz(14, 21);
    }

    .slide-item {
      aspect-ratio: 375/450;
    }

    .slick-dots {
      bottom: rem(30);
      transform: translateY(0);
      right: 12px;

      li {
        width: 32px;
        height: 32px;
        margin: 8px 0;

        &:before {
          font-size: rem(12);
        }
      }
    }

    .c-indicator__circle {
      width: 32px;
      height: 32px;
    }
  }
}

.okk-t-links {
  position: relative;
  z-index: 2;
  margin-top: rem(-97);
  padding-bottom: rem(60);

  &__inner {
    max-width: rem(966);
    margin: 0 auto;
  }

  &__box {
    background: #fff;
    border-radius: rem(25);
    padding: rem(20) rem(10);
  }

  &__row {
    display: flex;
    justify-content: center;
  }

  &__item {
    width: rem(315);
    max-width: 33.333333%;
    padding: 0 rem(10);
  }

  &__link {
    padding-bottom: rem(8);
    display: block;

    .links-img {
      aspect-ratio: 295/190;
      border-radius: rem(12);
      margin-bottom: rem(10);
      @extend .okk-img-fit;
    }

    .links-copy {
      @include fz(14, 20);
      font-weight: 500;
      text-decoration: underline;
    }

    @include pc {
      transition: $TRANSITION;

      &:hover {
        opacity: $OPACITY_HOVER;

        .links-copy {
          text-decoration: none;
        }
      }
    }
  }

  .slick-arrow {
    width: 4rem;
    height: 4rem;
    background: #fff no-repeat center center / rem(12.08) rem(8.43);
    border: 1px solid $COLOR_OKK_BASE;
    border-radius: 50%;
    z-index: 2;

    &.slick-prev {
      left: rem(-40);
      background-image: url("../img/common/ico_arrow_left.png");
    }

    &.slick-next {
      right: rem(-40);
      background-image: url("../img/common/ico_arrow_right.png");
    }

    &::before {
      content: none;
    }
  }

  @include sp {
    margin-top: 0;
    padding: rem(20) 0 rem(30);
    padding: rem(20) 0;

    .okk-inner {
      padding: 0;
    }

    &__box {
      padding: rem(10) 0;
    }

    &__item {
      width: rem(300);
      max-width: rem(300);

      @include sm {
        width: rem(178);
      }
    }

    &__link {
      padding-bottom: rem(8);
      display: block;

      .links-img {
        aspect-ratio: 158/105;
        margin-bottom: rem(8);
      }

      .links-copy {
        @include fz(12, 18);
      }
    }

    .slick-arrow {
      top: rem(70);
      transform: translateY(0);
      bottom: auto;

      &.slick-prev {
        left: calc(50vw - #{rem(140)} - #{rem(10)} - #{rem(40)});
      }

      &.slick-next {
        right: calc(50vw - #{rem(140)} - #{rem(10)} - #{rem(40)});
      }

      @include sm {
        top: rem(30);

        &.slick-prev {
          left: calc(50vw - #{rem(79)} - #{rem(10)} - #{rem(40)});
        }

        &.slick-next {
          right: calc(50vw - #{rem(79)} - #{rem(10)} - #{rem(40)});
        }
      }
    }
  }
}

.okk-t-point {
  overflow: hidden;

  &__list {
    display: flex;
    margin: rem(-10) 0 0 rem(-10);
  }

  &__item {
    width: rem(290);
    padding: rem(10);
  }

  .point-img {
    aspect-ratio: 270/164;
    @extend .okk-img-fit;
  }

  .point-desc {
    padding: rem(24) rem(12) rem(28);
  }

  .point-ttl {
    font-weight: 700;
    padding-right: rem(40);
  }

  &__link {
    background: #fff;
    border-radius: rem(12);
    overflow: hidden;
    box-shadow: $SHADOW_BASE;
    display: block;
    height: 100%;
    position: relative;
    @extend .hover;

    &::after {
      position: absolute;
      content: "";
      z-index: 1;
      width: rem(40);
      height: rem(40);
      bottom: rem(30);
      right: rem(12);
      background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center / rem(12.08) rem(8.43);
      border: 1px solid $COLOR_OKK_BASE;
      border-radius: 50%;
    }
  }

  @include sp {
    &__list {
      margin: rem(-10) 0 rem(-10) rem(-10);
    }

    &__item {
      width: rem(257);
      padding: rem(10);
    }

    .point-img {
      aspect-ratio: 237/131;
    }

    .point-desc {
      padding: rem(14) rem(12) rem(18);
    }

    &__link {
      &::after {
        width: rem(32);
        height: rem(32);
        bottom: rem(24);
        background-size: rem(10) rem(6.9784768212);
      }
    }
  }
}

.okk-t-movie {
  margin-top: rem(60);
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  &__item {
    width: rem(460);
    max-width: 100%;
  }

  &__btn {
    display: block;
    position: relative;
    overflow: hidden;
    box-shadow: $SHADOW_BASE;
    border-radius: rem(20);
    @extend .hover;

    .moive-bg {
      width: 100%;
      height: rem(134);
      position: relative;
      @extend .okk-img-fit;

      &::after {
        position: absolute;
        inset: 0;
        content: "";
        z-index: 1;
        background-image: linear-gradient(to right,
            rgba(#413e3e, 0),
            rgba(#413e3e, 0.54));
        mix-blend-mode: multiply;
      }
    }

    .movie-desc {
      position: absolute;
      inset: rem(4);
      border: 2px solid #fff;
      border-radius: rem(18);
      z-index: 2;
      display: flex;
      align-items: center;
    }

    .moive-ico {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: rem(57);
      height: rem(57);
      box-shadow: $SHADOW_BASE;
      border-radius: 50%;
    }

    .movie-ttl {
      margin-left: auto;
      margin-right: rem(20);
      min-width: rem(152);
      text-align: center;
      color: #fff;

      p {
        line-height: 2.375;
      }

      h3 {
        border-top: 1px solid #fff;
        @include fz(20, 36);
      }
    }
  }

  &:first-child {
    margin-top: 0 !important;
  }

  @include sp {
    margin-top: rem(30);

    &__item {
      width: rem(335);
    }

    &__btn {
      border-radius: rem(18);

      .moive-bg {
        height: rem(120);
      }

      .movie-desc {
        inset: rem(3);
        border-radius: rem(16);
      }

      .moive-ico {
        width: rem(42);
        height: rem(42);
      }

      .movie-ttl {
        margin-right: rem(12);
        min-width: rem(119);

        p {
          @include fz(12, 28);
        }

        h3 {
          @include fz(16, 28);
        }
      }
    }
  }
}

.okk-video-popup {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: $TRANSITION;

  &__content {
    position: relative;
    margin: rem(40) auto;
    padding: rem(40);
    width: rem(1060);
    max-width: 94%;
    background: $COLOR_OKK_BLUE_LIGHT;
    border-radius: rem(20);
  }

  .close-btn {
    position: absolute;
    top: 0;
    right: rem(10);
    color: $COLOR_OKK_BASE;
    font-size: rem(30);
    font-weight: bold;
    cursor: pointer;
    @extend .hover;
  }

  iframe {
    aspect-ratio: 16/9;
    display: block;
    border-radius: rem(12);
    overflow: hidden;
  }

  &.is-active {
    opacity: 1;
    visibility: visible;
  }

  @include sp {
    &__content {
      margin: rem(4) auto;
      padding: rem(4);
      border-radius: rem(12);
    }

    .close-btn {
      top: rem(-35);
      right: 0;
      line-height: 1;
      color: #fff;
    }

    iframe {
      border-radius: rem(12);
    }
  }
}

.okk-t-rentalplan {
  padding-bottom: rem(90);
  overflow: hidden;

  &__content {}

  &__list {
    display: flex;
    margin: rem(-10) 0 0 rem(-19);
  }

  &__item {
    width: rem(360);
    padding: rem(10) rem(19) rem(6);
  }

  &__link {
    display: block;
    height: 100%;
    position: relative;
    @extend .hover;

    .rentalplan-no {
      position: absolute;
      z-index: 2;
      top: rem(-9);
      left: rem(-23);
      left: rem(-19);
      border-radius: 50%;
      background: $COLOR_OKK_BASE;
      color: #fff;
      width: rem(75);
      height: rem(75);
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 1.25;
      font-weight: 700;
      text-align: center;
    }

    .rentalplan-img {
      position: relative;

      figure {
        aspect-ratio: 360/406;
        border-radius: rem(25);
        @extend .okk-img-fit;
      }
    }

    .rentalplan-tags {
      position: absolute;
      bottom: rem(13);
      left: rem(13);
      display: flex;
      gap: rem(4);
      flex-wrap: wrap;

      li {
        @include fz(12, 17);
        font-weight: 500;
        border-radius: rem(20);
        background-color: #fff;
        padding: rem(7) rem(9);
        display: inline-block;
      }
    }

    .rentalplan-desc {
      padding: rem(12) 0 rem(30);
    }

    .rentalplan-ttl {
      @include fz(36, 52);
      font-weight: 700;
    }

    .rentalplan-price {
      display: flex;
      align-items: center;
      @include fz(26, 37);
      font-weight: 700;
      gap: rem(6);

      .price-old {
        margin-top: rem(3);
        color: #868686;
        @include fz(18, 30);
        padding-right: rem(17);
        position: relative;

        &::after {
          position: absolute;
          content: "";
          z-index: 1;
          background: url("../img/common/ico_arrow_left_gray.png") no-repeat right center / cover;
          width: rem(6.5);
          height: rem(11);
          top: 55%;
          right: rem(3);
          transform: translateY(-50%);
        }
      }

      .tax {
        margin-top: rem(5);
        @include fz(14, 20);
        font-weight: 400;
      }
    }

    .rentalplan-copy {
      margin-top: rem(20);
      @include fz(14, 26);
      font-weight: 400;
    }

    .rentalplan-btn {
      max-width: rem(320);
      margin: 0 auto;

      .okk-btn {
        padding-top: rem(16);
        padding-bottom: rem(16);
      }
    }
  }

  @include sp {
    padding-bottom: rem(60);

    &__list {
      margin: rem(-13) 0 rem(10) rem(-13);
    }

    &__item {
      width: rem(296);
      padding: rem(13) rem(13) 0;
    }

    &__link {
      .rentalplan-no {
        top: rem(-13);
        left: rem(-13);
        width: rem(60);
        height: rem(60);
        @include fz(12, 17);
      }

      .rentalplan-img {
        figure {
          aspect-ratio: 270/290;
          border-radius: rem(18);
        }
      }

      .rentalplan-tags {
        bottom: rem(10);
        left: rem(10);
        gap: rem(6);
      }

      .rentalplan-desc {
        padding: rem(12) 0 rem(20);
      }

      .rentalplan-ttl {
        @include fz(24, 35);
      }

      .rentalplan-price {
        @include fz(20, 29);
        gap: rem(4);

        .price-old {
          margin-top: rem(3);
          @include fz(14, 20);
          padding-right: rem(18);
        }

        .tax {
          margin-top: rem(3);
          @include fz(12, 17);
        }
      }

      .rentalplan-copy {
        margin-top: rem(10);
      }

      .rentalplan-btn {
        max-width: rem(250);

        .okk-btn {
          padding-top: rem(13);
          padding-bottom: rem(13);
          @include fz(14, 20);
        }
      }
    }
  }
}

.okk-ls-services {
  margin-top: rem(60);
  background: $COLOR_OKK_GRAY_LIGHT;
  padding: rem(42);
  border-radius: rem(20);

  .okk-section-head {
    margin-bottom: rem(20);
  }

  &__ttl {
    @include fz(24, 35);
    font-weight: 700;
    margin-bottom: rem(20);
    color: $COLOR_OKK_GRAY_LIGHT2;
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: rem(20);
  }

  &__item {
    width: calc((100% - #{rem(40)}) / 3);

    &__inner {
      display: flex;
      align-items: center;
      padding: rem(20);
      background: #fff;
      border-radius: rem(12);
      height: 100%;
    }
  }

  &__ico {
    width: rem(61);
    flex-shrink: 0;
  }

  &__desc {
    flex-grow: 1;
    padding-left: rem(20);
  }

  &__hdg {
    margin-bottom: rem(8);
    @include fz(18, 26);
    font-weight: 700;
  }

  &__copy {
    @include fz(14, 24);
    font-weight: 400;
  }

  @include sp {
    margin-top: rem(50);
    padding: rem(20);
    border-radius: rem(12);

    &__ttl {
      @include fz(18, 26);
      margin-bottom: rem(16);
    }

    &__list {
      gap: rem(10);
    }

    &__item {
      width: calc(50% - #{rem(5)});

      @include sm {
        width: 100%;
      }

      &__inner {
        padding: rem(12) rem(10) rem(14) rem(20);
        border-radius: rem(10);
        height: auto;
      }
    }

    &__ico {
      width: rem(33);
    }

    &__desc {
      padding-left: rem(14);
    }

    &__hdg {
      margin-bottom: rem(3);
      @include fz(16, 24);
    }

    &__copy {
      @include fz(12, 17);
    }
  }
}

.okk-t-hairset {
  &__content {}

  &__photo {
    display: flex;
    gap: rem(20);
    flex-wrap: wrap;

    li {
      width: calc((100% - #{rem(60)}) / 4);
      aspect-ratio: 274/253;
      border-radius: rem(12);
      @extend .okk-img-fit;
    }
  }

  &__desc {
    margin-top: rem(40);
    border-radius: rem(20);
    background: #fff;
    padding: rem(40);
  }

  &__row {
    display: flex;
    flex-wrap: wrap;
    gap: rem(22);
  }

  &__item {
    width: calc((100% - #{rem(44)}) / 3);
    background: $COLOR_OKK_GRAY_LIGHT;
    border-radius: rem(12);

    &__inner {
      padding: rem(20);
    }
  }

  .hairset-ttl {
    @include fz(24, 35);
    font-weight: 700;
    color: $COLOR_OKK_RED;
  }

  .hairset-price {
    @include fz(24, 35);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: rem(6);

    .tax {
      @include fz(16, 24);
      font-weight: 400;
      margin-top: rem(4);
    }
  }

  @include sp {
    &__photo {
      gap: rem(11);

      li {
        width: calc(50% - #{rem(5.5)});
        aspect-ratio: 162/148;
        border-radius: rem(10);
      }
    }

    &__desc {
      margin-top: rem(40);
      border-radius: rem(12);
      padding: rem(20);
    }

    &__row {
      gap: rem(10);
    }

    &__item {
      border-radius: rem(10);
      width: calc(50% - #{rem(5)});

      @include sm {
        width: 100%;
      }

      &__inner {
        padding: rem(14) rem(20) rem(13);
      }
    }

    .hairset-ttl {
      @include fz(16, 24);
    }

    .hairset-price {
      @include fz(20, 29);
      gap: rem(10);

      .tax {
        @include fz(12, 17);
        margin-top: rem(3);
      }
    }
  }
}

.okk-slider-gallery {
  &__photo {
    display: flex;

    .slider-item {
      width: rem(475);
      padding: 0 rem(10);
    }

    figure {
      aspect-ratio: 455/419;
      border-radius: rem(25);
      @extend .okk-img-fit;
    }
  }

  @include sp {
    &__photo {
      .slider-item {
        width: rem(210);
        padding: 0 rem(10);
      }

      figure {
        aspect-ratio: 196/180;
        border-radius: rem(18);
      }
    }
  }
}

.okk-t-returnplan {
  &__content {}

  &__desc {
    border-radius: rem(20);
    background: #fff;
    padding: rem(40);
  }

  &__row {
    display: flex;
    flex-wrap: wrap;
    gap: rem(22);
  }

  &__item {
    width: calc((100% - #{rem(44)}) / 3);
    background: $COLOR_OKK_GRAY_LIGHT;
    border-radius: rem(12);

    &__inner {
      padding: rem(20);
    }
  }

  .returnplan-ttl {
    @include fz(24, 35);
    font-weight: 700;
    color: $COLOR_OKK_RED;
  }

  .returnplan-price {
    @include fz(24, 35);
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: rem(6);

    .tax {
      @include fz(16, 24);
      font-weight: 400;
      margin-top: rem(4);
    }
  }

  @include sp {
    &__desc {
      border-radius: rem(12);
      padding: rem(20);
    }

    &__row {
      gap: rem(10);
    }

    &__item {
      border-radius: rem(10);
      width: calc(50% - #{rem(5)});

      @include sm {
        width: 100%;
      }

      &__inner {
        padding: rem(14) rem(20) rem(13);
      }
    }

    .returnplan-ttl {
      @include fz(16, 24);
    }

    .returnplan-price {
      @include fz(20, 29);
      gap: rem(10);

      .tax {
        @include fz(12, 17);
        margin-top: rem(3);
      }
    }
  }
}

.okk-ls-storeinfo {
  padding: rem(60) 0;

  &__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;
    gap: rem(30);
  }

  &__item {
    width: calc((100% - #{rem(90)}) / 4);
    box-shadow: $SHADOW_BASE;
    border-radius: rem(12);
    background: #fff;
    overflow: hidden;

    @include pc {
      min-width: rem(266);
    }
  }

  &__link {
    display: block;
    @extend .hover;

    .storeinfo-img {
      aspect-ratio: 274/180;
      @extend .okk-img-fit;
      box-shadow: $SHADOW_BASE;
    }

    .storeinfo-txt {
      margin-top: rem(14);
      margin-bottom: rem(14);
      text-align: center;
      @include fz(20, 28);
      font-weight: 700;

      .fz-sm {
        @include fz(16, 25);
      }
    }

    .storeinfo-btn {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .okk-btn {
      @include fz(12, 16);
      padding: rem(5) rem(25);
      width: rem(208);

      &::after {
        right: rem(14);
        width: rem(10);
        height: rem(7.82);
      }
    }
  }

  .storeinfo-description {
    padding: rem(18) rem(14);
    @include fz(12, 20);

    p {
      color: $COLOR_OKK_GRAY_LIGHT2;
      margin-bottom: rem(5);
    }
  }

  .storeinfo-features {
    dt {
      position: relative;
      display: inline-flex;
      cursor: pointer;
      text-decoration: underline;

      @include hover {
        &:hover {
          text-decoration: none;
        }
      }

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        width: rem(14);
        height: rem(14);
        border: 1px solid $COLOR_OKK_BASE;
        border-radius: 50%;
      }

      span {
        padding-right: rem(17);
        position: relative;
        text-underline-offset: 2px;

        &::before,
        &::after {
          content: "";
          position: absolute;
          top: 50%;
          right: rem(4);
          transform: translateY(-50%);
          width: rem(6);
          height: 1px;
          background: $COLOR_OKK_BASE;
        }

        &::after {
          opacity: 1;
          transition: $TRANSITION;
          transform: translateY(-50%) rotate(90deg);
        }
      }

      &.is-active {
        span::after {
          opacity: 0;
        }
      }
    }

    dd {
      margin-top: rem(12);
      background: $COLOR_OKK_RED_LIGHT;
      padding: rem(13) rem(10);
      border-radius: rem(4);
    }
  }

  @include sp {
    padding-top: 5rem;

    &__list {
      flex-direction: column;
      gap: rem(18);
    }

    &__item {
      width: 100%;
    }

    &__link {
      display: flex;
      align-items: flex-end;
      gap: rem(14);
      padding: rem(16) rem(12) 0;

      .storeinfo-img {
        width: rem(160);
        max-width: 50%;
        flex-shrink: 0;
        aspect-ratio: 160/114;
        border-radius: rem(8);
        overflow: hidden;
      }

      .storeinfo-link-info {
        flex-grow: 1;
      }

      .storeinfo-txt {
        margin-top: 0;
        margin-bottom: rem(14);
        text-align: left;
        @include fz(16, 24);

        .fz-sm {
          @include fz(12, 24);
        }
      }

      .storeinfo-btn {
        justify-content: flex-start;
      }

      .okk-btn {
        padding: rem(4) rem(20);
        width: rem(137);
        max-width: 100%;

        &::after {
          right: rem(12);
        }
      }
    }

    .storeinfo-description {
      padding: rem(10) rem(12) rem(16);
      @include fz(12, 20);

      p {
        color: $COLOR_OKK_GRAY_LIGHT2;
        margin-bottom: rem(4);
      }
    }

    .storeinfo-features {
      padding-top: rem(6);

      dd {
        padding: rem(12) rem(12);
      }
    }
  }
}

.okk-store-other {
  padding: rem(80) 0;
  overflow: hidden;

  &__box {
    margin-top: 0;
    max-width: rem(966);
    margin: 0 auto;
    background: #fff;
    border-radius: rem(25);
    padding: rem(20) rem(10);

    @include pc {
      background: $COLOR_OKK_BLUE_LIGHT;

      .okk-bg-gray & {
        background: #fff;
      }
    }
  }

  &__list {
    justify-content: center;
    display: flex;
  }

  &__item {
    width: rem(315);
    max-width: 33.333333%;
    padding: 0 rem(10);
  }

  &__link {
    padding-bottom: rem(8);
    display: block;

    .img {
      aspect-ratio: 295/190;
      border-radius: rem(12);
      margin-bottom: rem(10);
      @extend .okk-img-fit;
    }

    .txt {
      @include fz(14, 20);
      font-weight: 500;
      text-decoration: underline;
    }

    @include pc {
      transition: $TRANSITION;

      &:hover {
        opacity: $OPACITY_HOVER;

        .txt {
          text-decoration: none;
        }
      }
    }
  }

  .slick-arrow {
    width: 4rem;
    height: 4rem;
    background: #fff no-repeat center center / rem(12.08) rem(8.43);
    border: 1px solid $COLOR_OKK_BASE;
    border-radius: 50%;
    z-index: 2;

    &.slick-prev {
      left: rem(-40);
      background-image: url("../img/common/ico_arrow_left.png");
    }

    &.slick-next {
      right: rem(-40);
      background-image: url("../img/common/ico_arrow_right.png");
    }

    &::before {
      content: none;
    }
  }

  @include sp {
    padding: rem(40) 0;

    &__box {
      margin: 0 rem(-20);
      padding: 0;
      background: transparent;
    }

    &__item {
      width: rem(300);
      max-width: rem(300);

      @include sm {
        width: rem(178);
      }
    }

    &__link {
      padding-bottom: 0;
      display: block;

      .storeinfo-other-img {
        aspect-ratio: 158/105;
        margin-bottom: rem(8);
      }

      .storeinfo-other-txt {
        @include fz(12, 18);
      }
    }

    .slick-arrow {
      top: rem(70);
      transform: translateY(0);
      bottom: auto;

      &.slick-prev {
        left: calc(50vw - #{rem(140)} - #{rem(10)} - #{rem(40)});
      }

      &.slick-next {
        right: calc(50vw - #{rem(140)} - #{rem(10)} - #{rem(40)});
      }

      @include sm {
        top: rem(30);

        &.slick-prev {
          left: calc(50vw - #{rem(79)} - #{rem(10)} - #{rem(40)});
        }

        &.slick-next {
          right: calc(50vw - #{rem(79)} - #{rem(10)} - #{rem(40)});
        }
      }
    }
  }

  .okk-t-store-other & {
    padding-top: 2rem;
  }
}

.okk-t-about {
  &__inner {
    position: relative;
  }

  &__content {
    padding: rem(60) rem(280);
    position: relative;
    z-index: 2;
  }

  &__img {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    list-style: none;

    li {
      width: rem(264);
      height: 100%;
      overflow: hidden;
      position: relative;

      span {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-position: center;
        background-repeat: repeat-y;
        background-size: 100% rem(1253);
        z-index: 1;
      }

      &:nth-child(1) {
        span {
          animation: 25s linear infinite scrollAboutDown;
          background-image: url("../img/home/<USER>");
        }
      }

      &:nth-child(2) {
        span {
          animation: 25s linear infinite scrollAboutUp;
          background-image: url("../img/home/<USER>");
        }
      }
    }
  }

  @include sp {
    padding: rem(40) 0;

    .okk-copy {
      text-align: center;
    }

    &__content {
      padding: rem(163) 0;
    }

    &__img {
      flex-direction: column;
      margin: 0 rem(-20);
      width: calc(100% + #{rem(40)});

      li {
        width: 100%;
        height: rem(133);

        span {
          background-repeat: repeat-x;
          background-size: rem(687) 100%;
        }

        &:nth-child(1) {
          span {
            animation: 25s linear infinite scrollAboutRight;
            background-image: url("../img/home/<USER>");
          }
        }

        &:nth-child(2) {
          span {
            animation: 25s linear infinite scrollAboutLeft;
            background-image: url("../img/home/<USER>");
          }
        }
      }
    }
  }
}

.okk-t-column {
  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: rem(38);
  }

  &__item {
    width: calc((100% - #{rem(76)}) / 3);
  }

  &__link {
    @extend .hover;

    .column-img {
      aspect-ratio: 360/270;
      @extend .okk-img-fit;
      border-radius: rem(12);
    }

    .column-cat {
      margin-top: rem(14);
      display: flex;
      flex-wrap: wrap;
      gap: rem(6);

      li {
        border: 1px solid $COLOR_OKK_RED;
        background: #fff;
        color: $COLOR_OKK_RED;
        @include fz(12, 17);
        font-weight: 500;
        padding: rem(6) rem(8);
        border-radius: rem(31);
      }
    }

    .column-txt {
      margin-top: rem(10);
      line-height: 1.75;
      font-weight: 700;
    }
  }

  @include sp {
    &__list {
      gap: rem(20);
    }

    &__item {
      width: calc(50% - #{rem(10)});

      @include sm {
        width: 100%;
      }
    }

    &__link {
      .column-img {
        aspect-ratio: 335/200;
      }

      .column-txt {
        margin-top: rem(8);
        @include fz(16, 24);
        text-decoration: underline;
      }
    }
  }
}

.okk-t-formal {
  &__content {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__item {
    width: rem(460);
  }

  &__btn {
    display: block;
    position: relative;
    overflow: hidden;
    box-shadow: $SHADOW_BASE;
    border-radius: rem(20);
    @extend .hover;

    .formal-bg {
      width: 100%;
      height: rem(134);
      position: relative;
      @extend .okk-img-fit;

      &::after {
        position: absolute;
        inset: 0;
        content: "";
        z-index: 1;
        background-image: linear-gradient(to bottom,
            rgba(#413e3e, 0),
            rgba(#413e3e, 0.54));
        mix-blend-mode: multiply;
      }
    }

    .formal-ico {
      position: absolute;
      z-index: 1;
      width: rem(40);
      height: rem(40);
      bottom: rem(16);
      right: rem(16);
      background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center / rem(12.08) rem(8.43);
      border: 1px solid $COLOR_OKK_BASE;
      border-radius: 50%;
    }

    .formal-desc {
      position: absolute;
      inset: rem(4);
      border: 2px solid #fff;
      border-radius: rem(18);
      z-index: 2;
      display: flex;
      align-items: flex-end;
    }

    .formal-txt {
      text-align: center;
      color: #fff;
      width: 100%;
      padding-bottom: rem(15);

      p {
        line-height: 1.5;
      }

      h3 {
        @include fz(20, 24);
      }
    }
  }

  @include sp {
    &__item {
      width: rem(335);
      max-width: 100%;
    }

    &__btn {
      border-radius: rem(18);

      .formal-bg {
        height: rem(120);
      }

      .formal-ico {
        width: rem(32);
        height: rem(32);
        bottom: rem(12);
        right: rem(12);
        background-size: rem(10) rem(6.978476821);
      }

      .formal-desc {
        inset: rem(3);
        border: 2px solid #fff;
        border-radius: rem(16);
      }

      .formal-txt {
        padding-bottom: rem(12);

        p {
          @include fz(12, 17);
        }

        h3 {
          @include fz(14, 20);
        }
      }
    }
  }
}

.okk-t-publication {
  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: rem(38);
  }

  &__item {
    width: calc((100% - #{rem(76)}) / 3);
  }

  &__link {
    @extend .hover;

    .publication-img {
      aspect-ratio: 360/270;
      @extend .okk-img-fit;
      border-radius: rem(12);
    }

    .publication-time {
      margin-top: rem(20);
      color: $COLOR_OKK_RED;
      @include fz(12, 17);
      font-weight: 400;
    }

    .publication-txt {
      margin-top: rem(10);
      line-height: 1.75;
      font-weight: 700;
    }
  }

  @include sp {
    &__list {
      gap: rem(30) rem(20);
      justify-content: space-between;
    }

    &__item {
      width: calc(50% - #{rem(10)});

      @include sm {
        width: 100%;
      }
    }

    &__link {
      .publication-img {
        aspect-ratio: 335/200;
      }

      .publication-time {
        margin-top: rem(14);
      }

      .publication-txt {
        margin-top: rem(8);
        @include fz(16, 24);
        text-decoration: underline;
      }
    }
  }
}

.okk-t-cover-woman {
  padding-top: rem(60);
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  &__item {
    width: rem(478);
  }

  &__bnr {
    border-radius: rem(12);
    display: block;
    @extend .okk-img-fit;
    @extend .hover;
  }

  @include sp {
    padding-top: rem(36);

    &__item {
      width: rem(351);
      max-width: calc(100% + #{rem(16)});
      margin: 0 rem(-8);
    }
  }
}

.okk-t-news {
  background: #fffbf2;

  &__content {
    max-width: rem(965);
    margin: 0 auto;
  }

  &__list {
    border-top: 1px solid $COLOR_OKK_BORDER;
  }

  &__item {
    border-bottom: 1px solid $COLOR_OKK_BORDER;
  }

  &__link {
    padding: rem(25) 0;
    display: block;

    @include pc {
      &:hover {
        .news-desc {
          text-decoration: underline;
        }
      }
    }
  }

  &__inner {
    display: flex;
    align-items: center;
  }

  .news-time {
    width: rem(177);
    flex-shrink: 0;
    font-weight: 700;
    color: $COLOR_OKK_RED;
    padding-left: rem(10);
  }

  .news-desc {
    flex-grow: 1;
    font-weight: 400;
  }

  @include sp {
    &__link {
      padding: rem(15) 0;
    }

    &__inner {
      display: block;
    }

    .news-time {
      width: 100%;
      padding-left: 0;
    }

    .news-desc {
      margin-top: rem(8);
      text-decoration: underline;
    }
  }
}

.okk-t-corporate {
  padding-bottom: rem(90);

  .okk-section-head {
    margin-bottom: rem(20);
  }

  .okk-headline .txt {
    font-size: rem(26);
  }

  &__content {
    max-width: rem(966);
    margin: 0 auto;
    border-radius: rem(25);
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(40);
  }

  &__list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: rem(30) rem(40);
  }

  &__item {
    width: rem(412);
    max-width: calc((50% - #{rem(20)}));
  }

  &__inner {
    display: flex;
    align-items: center;
    gap: rem(12);
  }

  &__img {
    aspect-ratio: 1/1;
    border-radius: rem(6);
    @extend .okk-img-fit;
    width: rem(106);
    flex-shrink: 0;
  }

  &__desc {
    flex-grow: 1;
    padding-right: rem(30);
  }

  &__copy {
    @include fz(14, 20);
    font-weight: 400;
  }

  &__ttl {
    @include fz(24, 35);
    font-weight: 700;
    margin-top: rem(5);
  }

  &__link {
    padding: rem(7);
    display: block;
    background: #fff;
    border-radius: rem(12);
    box-shadow: $SHADOW_BASE;
    @extend .hover;

    position: relative;

    &::after {
      position: absolute;
      top: 50%;
      right: rem(20);
      transform: translateY(-50%);
      z-index: 1;
      content: "";
      background: no-repeat center center / 100% 100%;
      width: rem(19);
      height: rem(19);
    }

    &[target="_blank"] {
      &::after {
        background-image: url("../img/common/ico_extend.png");
      }
    }
  }

  @include sp {
    padding-bottom: rem(60);

    .okk-headline .txt {
      font-size: rem(18);
    }

    &__content {
      padding: rem(20);
      border-radius: rem(12);
    }

    &__list {
      gap: rem(10);
    }

    &__item {
      max-width: calc(50% - #{rem(5)});

      @include sm {
        max-width: 100%;
      }
    }

    &__inner {
      gap: rem(10);
    }

    &__img {
      width: rem(66);
    }

    &__desc {
      padding-right: rem(16);
    }

    &__copy {
      @include fz(12, 17);
    }

    &__ttl {
      @include fz(18, 26);
      margin-top: rem(3);
    }

    &__link {
      border-radius: rem(10);

      &::after {
        right: rem(10);
        width: rem(14);
        height: rem(14);
      }
    }
  }
}

// For pagination /news/
.link-pages {
  margin-top: 4rem;
  border-top: 1px solid #c1c1c1;
  border-bottom: 1px solid #c1c1c1;
  padding: 3rem 0;
  display: flex;
  justify-content: center;
  gap: 2rem;

  >a {
    color: #999999;
    font-size: 1.6rem;
    line-height: 1.75;
    padding: 0.5rem;
    width: 4rem;
    border: 1px solid #c1c1c1;
    border-radius: 50%;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #fff;

    &.active {
      background-color: $COLOR_OKK_RED;
      color: #fff;
      border-color: $COLOR_OKK_RED;
    }

    &:hover {
      border-color: $COLOR_OKK_RED;
    }
  }

  .nav {
    width: auto;
    padding: 0.5rem 3rem;
    border-radius: 2rem;

    &.prev {
      padding: 0.5rem 2.3rem 0.5rem 3.9rem;
      background: url("/common/img/arr_gray01.png") no-repeat left 1.4rem center #fff;
      background-size: 1rem 1.5rem;
    }

    &.next {
      padding: 0.5rem 3.9rem 0.5rem 2.3rem;
      background: url("/common/img/arr_gray02.png") no-repeat right 1.4rem center #fff;
      background-size: 1rem 1.5rem;
    }
  }

  @include sp {
    margin-top: 3rem;
    padding: 1.5rem 0;
    gap: 1rem;

    >a {
      font-size: 1rem;
      line-height: 1;
      padding: 0.6rem 0.5rem;
      width: 2.4rem;
    }

    .nav {
      padding: 0.5rem 3rem;
      border-radius: 2rem;

      &.prev {
        padding: 0.5rem 1.5rem 0.5rem 2.1rem;
        background-position: 0.8rem center;
        background-size: 0.6rem 0.9rem;
      }

      &.next {
        padding: 0.5rem 2.1rem 0.5rem 1.5rem;
        background-position: right 0.8rem center;
        background-size: 0.6rem 0.9rem;
      }
    }
  }
}

// For storeinfo
.modal-wrap {
  display: none;
}

.okk-breadcrumb {
  padding: rem(20) 0;

  ul {
    display: flex;
    flex-wrap: wrap;
    gap: rem(10);
  }

  li {
    @include fz(14, 20);
    font-weight: 500;

    &:not(:last-child) {
      padding-right: rem(14);
      position: relative;

      &::after {
        position: absolute;
        content: "";
        width: rem(6.5);
        height: rem(11);
        top: 55%;
        right: 0;
        transform: translateY(-50%);
        background: url("../img/common/ico_arrow_breadcrumb.png") no-repeat right center / cover;
        z-index: 1;
      }
    }
  }

  a {
    text-decoration: underline;
    text-underline-offset: 2px;
    color: inherit;

    @include pc {
      &:hover {
        text-decoration: none;
      }
    }
  }

  &+.okk-section-head {
    margin-top: rem(20);
  }

  @include sp {
    padding: rem(13) 0 rem(20);

    li {
      @include fz(12, 17);

      &:not(:last-child) {
        &::after {
          width: rem(5.5);
          height: rem(9.75);
        }
      }
    }
  }
}

.okk-storeinfo-mv {
  position: relative;

  &__bg {
    overflow: hidden;

    @include pc {
      height: rem(489);
    }

    @include sp {
      aspect-ratio: 375/201;
    }

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      object-position: top center;
    }
  }

  &__ttl {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    background: #fff;
    font-weight: 700;
    border-radius: rem(25) rem(25) 0 0;
    padding: rem(20) rem(30) rem(10);
    width: rem(543);
    text-align: center;
    @include fz(40, 50);
  }

  @include sp {
    &__ttl {
      border-radius: rem(15) rem(15) 0 0;
      padding: rem(15) rem(30) rem(8);
      width: rem(335);
      @include fz(26, 37);
    }
  }
}

.okk-mv {
  position: relative;

  &__bg {
    overflow: hidden;

    @include pc {
      height: rem(489);
    }

    @include sp {
      aspect-ratio: 375/201;
    }

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      object-position: top center;
    }
  }

  &__ttl {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    background: #fff;
    font-weight: 700;
    border-radius: rem(25) rem(25) 0 0;
    padding: rem(20) rem(30) rem(10);
    width: rem(543);
    text-align: center;
    @include fz(40, 50);
  }

  @include sp {
    &__ttl {
      border-radius: rem(15) rem(15) 0 0;
      padding: rem(15) rem(30) rem(8);
      width: rem(335);
      @include fz(26, 37);
    }
  }
}

.okk-store-name-top {
  border-top: 1px solid #fff;
  background: $COLOR_OKK_RED;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  @include fz(40, 46);
  height: rem(120);
  position: relative;

  .txt {
    max-width: rem(1196);
    margin: 0 auto;
    padding: 0 rem(20);
    text-align: center;
  }

  @include sp {
    @include fz(26, 35);
    height: rem(70);
  }
}

.okk-store-mv {
  margin-top: rem(10);

  &__bg {
    border-radius: rem(20);
    overflow: hidden;

    @include pc {
      height: rem(344);
    }

    @include sp {
      aspect-ratio: 335/180;
    }

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  &+.okk-section-head {
    padding-top: rem(30);
    padding-bottom: rem(10);

    .okk-headline {
      .txt {
        @include pc {
          @include fz(36, 52);
        }
      }
    }

    @include sp {
      padding-bottom: 0;
    }
  }
}

.okk-store-map {
  padding-bottom: rem(80);

  .okk-section-head {
    @include pc {
      margin-top: rem(45);
    }
  }

  &-note {
    margin-bottom: rem(40);

    ul {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: rem(8);
    }

    li {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: rem(8);
      line-height: 1.5;
      font-weight: 500;

      img {
        width: rem(21.57);
      }
    }
  }

  &-local {
    position: relative;

    &__iframe {
      width: 100%;
      border: 1px solid #707070;
      border-radius: rem(20);
    }

    &__point {
      --map-width: 1156;
      --map-height: 745;

      a {
        position: absolute;
        display: flex;
        transition: $TRANSITION;
        z-index: 1;
        opacity: 1;

        @include pc {
          &:hover {
            opacity: $OPACITY_HOVER;
            // transform: scale(1.1);
          }
        }
      }

      #map-store-btn-01 {
        width: rem(116.82);
        width: calc(116.82 / var(--map-width) * 100%);
        right: rem(308);
        right: calc(308 / var(--map-width) * 100%);
        bottom: rem(110);
        bottom: calc(110 / var(--map-height) * 100%);
      }

      #map-store-btn-02 {
        width: rem(115.94);
        width: calc(115.94 / var(--map-width) * 100%);
        right: rem(184);
        right: calc(184 / var(--map-width) * 100%);
        bottom: rem(150);
        bottom: calc(150 / var(--map-height) * 100%);
      }

      #map-store-btn-03 {
        width: rem(147.93);
        width: calc(147.93 / var(--map-width) * 100%);
        right: rem(530);
        right: calc(530 / var(--map-width) * 100%);
        bottom: rem(258);
        bottom: calc(258 / var(--map-height) * 100%);
      }

      #map-store-btn-04 {
        width: rem(125.07);
        width: calc(125.07 / var(--map-width) * 100%);
        right: rem(223);
        right: calc(223 / var(--map-width) * 100%);
        top: rem(210);
        top: calc(210 / var(--map-height) * 100%);
      }

      #map-store-btn-05 {
        width: rem(162);
        width: calc(162 / var(--map-width) * 100%);
        right: rem(345);
        right: calc(345 / var(--map-width) * 100%);
        top: rem(50);
        top: calc(50 / var(--map-height) * 100%);
      }

      #map-store-btn-06 {
        width: rem(135.17);
        width: calc(135.17 / var(--map-width) * 100%);
        left: rem(256);
        left: calc(256 / var(--map-width) * 100%);
        bottom: rem(178);
        bottom: calc(178 / var(--map-height) * 100%);
      }

      #map-store-btn-07 {
        width: rem(122.3);
        width: calc(122.3 / var(--map-width) * 100%);
        left: rem(249);
        left: calc(249 / var(--map-width) * 100%);
        top: rem(191);
        top: calc(191 / var(--map-height) * 100%);
      }

      #map-scenic-btn-01 {
        width: rem(160.08);
        width: calc(160.08 / var(--map-width) * 100%);
        right: rem(394);
        right: calc(394 / var(--map-width) * 100%);
        bottom: rem(14);
        bottom: calc(14 / var(--map-height) * 100%);
      }

      #map-scenic-btn-02 {
        width: rem(141.78);
        width: calc(141.78 / var(--map-width) * 100%);
        right: rem(410);
        right: calc(410 / var(--map-width) * 100%);
        bottom: rem(49);
        bottom: calc(49 / var(--map-height) * 100%);
      }

      #map-scenic-btn-03 {
        width: rem(142.41);
        width: calc(142.41 / var(--map-width) * 100%);
        right: rem(93);
        right: calc(93 / var(--map-width) * 100%);
        bottom: rem(50);
        bottom: calc(50 / var(--map-height) * 100%);
      }

      #map-scenic-btn-04 {
        width: rem(172.56);
        width: calc(172.56 / var(--map-width) * 100%);
        right: rem(172);
        right: calc(172 / var(--map-width) * 100%);
        bottom: rem(231);
        bottom: calc(231 / var(--map-height) * 100%);
      }

      #map-scenic-btn-05 {
        width: rem(187.07);
        width: calc(187.07 / var(--map-width) * 100%);
        right: rem(223);
        right: calc(223 / var(--map-width) * 100%);
        bottom: rem(318);
        bottom: calc(318 / var(--map-height) * 100%);
      }

      #map-scenic-btn-06 {
        width: rem(97.87);
        width: calc(97.87 / var(--map-width) * 100%);
        right: rem(238);
        right: calc(238 / var(--map-width) * 100%);
        bottom: rem(444);
        bottom: calc(444 / var(--map-height) * 100%);
      }

      #map-scenic-btn-07 {
        width: rem(37.55);
        width: calc(37.55 / var(--map-width) * 100%);
        right: rem(628);
        right: calc(628 / var(--map-width) * 100%);
        top: rem(125);
        top: calc(125 / var(--map-height) * 100%);
      }

      #map-scenic-btn-08 {
        width: rem(135.75);
        width: calc(135.75 / var(--map-width) * 100%);
        right: rem(209);
        right: calc(209 / var(--map-width) * 100%);
        top: rem(86);
        top: calc(86 / var(--map-height) * 100%);
      }

      #map-scenic-btn-09 {
        width: rem(150.27);
        width: calc(150.27 / var(--map-width) * 100%);
        left: rem(280);
        left: calc(280 / var(--map-width) * 100%);
        bottom: rem(82);
        bottom: calc(82 / var(--map-height) * 100%);
      }

      #map-scenic-btn-10 {
        width: rem(101.71);
        width: calc(101.71 / var(--map-width) * 100%);
        left: rem(277);
        left: calc(277 / var(--map-width) * 100%);
        bottom: rem(330);
        bottom: calc(330 / var(--map-height) * 100%);
      }

      #map-scenic-btn-11 {
        width: rem(119.37);
        width: calc(119.37 / var(--map-width) * 100%);
        left: rem(116);
        left: calc(116 / var(--map-width) * 100%);
        top: rem(195);
        top: calc(195 / var(--map-height) * 100%);
      }
    }
  }

  .okk-section-head+& {
    .okk-section-head:first-child {
      display: none;
    }
  }

  @include sp {
    padding-bottom: rem(60);

    &-note {
      margin-bottom: rem(20);

      ul {
        gap: rem(6);
      }

      li {
        gap: rem(6);

        img {
          width: rem(18.37);
        }
      }
    }

    &-local {
      &__iframe {
        border-radius: rem(12);
      }
    }
  }
}

.okk-map-modal {
  width: rem(680);
  margin: 0 auto;
  padding: rem(40);
  background: #fff;
  border-radius: rem(20);
  position: relative;

  &__ttl {
    text-align: center;
    @include fz(36, 52);
    font-weight: 700;
    margin-bottom: rem(20);
  }

  &__photo {
    width: 100%;
    overflow: hidden;
    border-radius: rem(25);

    a {
      transition: $TRANSITION;

      @include pc {
        &:hover {
          opacity: $OPACITY_HOVER;
        }
      }
    }

    img {
      width: 100%;
      display: block;
    }
  }

  &__btn {
    margin-top: rem(30);
    display: flex;
    justify-content: center;
    align-items: center;

    a {
      width: rem(320);
      padding: rem(16) rem(40);
    }
  }

  &__copy {
    margin-top: rem(17);
    font-weight: 500;
    line-height: 1.875;
  }

  &__slider {
    .slick-arrow {
      width: rem(66);
      height: rem(66);
      border: 2px solid $COLOR_OKK_BASE;
      background: #fff;
      border-radius: 50%;
      margin-top: rem(36);
      z-index: 2;
      @extend .hover;

      &::before {
        content: none;
      }

      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        content: "";
        z-index: 1;
        width: rem(15);
        height: rem(11.7272727273);
        background: no-repeat center center / 100% 100%;
      }

      &.slick-prev {
        left: rem(-126);

        &::after {
          background-image: url("../img/common/ico_arrow_left.png");
          background-position: center left;
        }
      }

      &.slick-next {
        right: rem(-126);

        &::after {
          background-image: url("../img/common/ico_arrow_right.png");
          background-position: center right;
        }
      }
    }
  }

  .slider-counter {
    position: absolute;
    bottom: rem(-52);
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    color: #fff;
    @include fz(20, 32);
    font-weight: 500;
  }

  .mfp-close {
    color: transparent;
    text-indent: -99999px;
    opacity: 1;
    top: rem(-25);
    right: rem(-100);
    width: rem(34);
    height: rem(25);
    background: url("../img/common/map/ico_close_map.svg") no-repeat center center / cover;
    transition: $TRANSITION;

    @include pc {
      &:hover {
        opacity: $OPACITY_HOVER;
      }
    }
  }

  @include sp {
    width: rem(500);
    padding: rem(20);
    border-radius: rem(18);

    &__ttl {
      @include fz(26, 36);
    }

    &__photo {
      border-radius: rem(12);
    }

    &__btn {
      margin-top: rem(20);

      a {
        width: rem(250);
        padding: rem(11) rem(40);
      }
    }

    &__copy {
      margin-top: rem(12);
    }

    &__slider {
      .slick-arrow {
        width: rem(40);
        height: rem(40);
        border-width: 1px;
        margin-top: 0;
        top: auto;
        bottom: rem(-80);
        transform: translate(0, 0);

        &::after {
          width: rem(12);
          height: rem(9.3818181818);
        }

        &.slick-prev {
          left: rem(60);
        }

        &.slick-next {
          right: rem(60);
        }
      }
    }

    .slider-counter {
      bottom: rem(-51);
      @include fz(16, 24);
    }

    .mfp-close {
      top: rem(-40);
      right: 0;
    }
  }

  @include sm {
    width: calc(100vw - 4rem);

    &__copy {
      margin-top: rem(10);
      font-size: rem(12);
    }
  }
}

.okk-store {
  padding-top: rem(40);

  &__unit {
    padding: 0 0 rem(100);
  }

  &__slider {
    overflow: hidden;

    .slide-item {
      padding: 0 rem(10);
      max-width: rem(620);
      height: rem(430);

      img {
        border-radius: rem(25);
        object-fit: cover;
        object-position: center;
        max-width: 100%;
        height: 100%;
      }
    }

    .slick-arrow {
      width: rem(66);
      height: rem(66);
      border: 2px solid $COLOR_OKK_BASE;
      background: #fff;
      border-radius: 50%;
      z-index: 2;
      @extend .hover;

      &::before {
        content: none;
      }

      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        content: "";
        z-index: 1;
        width: rem(15);
        height: rem(11.7272727273);
        background: no-repeat center center / 100% 100%;
      }

      &.slick-prev {
        left: 0;
        left: calc(50% - #{rem(310)} - #{rem(66)});

        &::after {
          background-image: url("../img/common/ico_arrow_left.png");
          background-position: center left;
        }
      }

      &.slick-next {
        right: 0;
        right: calc(50% - #{rem(310)} - #{rem(66)});

        &::after {
          background-image: url("../img/common/ico_arrow_right.png");
          background-position: center right;
        }
      }
    }
  }

  &__desc {
    padding-top: rem(40);
  }

  &__ttl {
    text-align: center;
    display: block;
    @include fz(32, 46);
    font-weight: 700;
    letter-spacing: 0;
  }

  &__copy {
    text-align: center;
    line-height: 1.875;
    font-weight: 500;
    letter-spacing: 0;
    margin-top: rem(20);
  }

  &__btn {
    margin-top: rem(40);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: rem(20);

    .okk-btn {
      width: rem(320);
    }
  }

  @include sp {
    padding-top: 0;

    &__unit {
      padding: 0 0 rem(60);
    }

    &__slider {
      .slide-item {
        max-width: rem(402);
        height: rem(274);

        img {
          border-radius: rem(12);
        }
      }

      .slick-arrow {
        width: rem(40);
        height: rem(40);
        border-width: 1px;

        &::after {
          width: rem(12);
          height: rem(9.3818181818);
        }

        &.slick-prev {
          left: calc(50% - #{rem(201)} - #{rem(40)});
        }

        &.slick-next {
          right: calc(50% - #{rem(201)} - #{rem(40)});
        }
      }
    }

    &__desc {
      padding-top: rem(20);
    }

    &__ttl {
      @include fz(26, 36);
    }

    &__btn {
      margin-top: rem(20);
      gap: rem(10);

      li {
        padding: 0 rem(5);
      }

      .okk-btn {
        width: rem(250);
        padding: rem(11) rem(40);
      }
    }
  }

  @include sm {
    &__slider {
      .slide-item {
        max-width: rem(257);
        height: rem(170);
      }

      .slick-arrow {
        &.slick-prev {
          left: calc(50% - #{rem(128.5)} - #{rem(40)});
        }

        &.slick-next {
          right: calc(50% - #{rem(128.5)} - #{rem(40)});
        }
      }
    }

    &__copy {
      text-align: left;
    }

    &__btn {
      li {
        padding: 0;
      }
    }
  }
}

.okk-store-access {
  padding-bottom: rem(100);

  &__img {
    border: 1px solid #707070;
    border-radius: rem(20);
    overflow: hidden;

    img {
      display: block;
    }
  }

  @include sp {
    padding-bottom: rem(60);

    .okk-section-head {
      margin-bottom: rem(20);
    }

    &__img {
      border-radius: rem(18);
    }
  }
}

.okk-store-detail {
  padding: rem(70) 0 rem(80);

  &__box {
    border-radius: rem(20);
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(40);
    display: flex;
    align-items: center;
    gap: rem(40);
  }

  &__pic {
    width: rem(519);
    flex-shrink: 0;
    border-radius: rem(12);
    overflow: hidden;
  }

  &__desc {
    flex-grow: 1;
    line-height: 1.875;
    font-weight: 500;
  }

  &__ttl {
    @include fz(24, 35);
    font-weight: 700;
    color: $COLOR_OKK_RED;
    margin-bottom: rem(20);
  }

  &__copy {
    dl {
      margin-top: rem(2);
      display: flex;
      flex-wrap: wrap;
      gap: 0 1em;

      dt {
        font-weight: 700;
      }
    }
  }

  @include sp {
    padding: rem(40) 0 rem(50);

    &__box {
      border-radius: rem(12);
      padding: rem(20) rem(20) rem(30);
      flex-direction: column;
      gap: rem(20);
    }

    &__pic {
      width: 100%;
      border-radius: rem(10);
    }

    &__ttl {
      @include fz(18, 26);
      margin-bottom: rem(14);
    }

    &__copy {
      dl {
        margin-top: rem(2);
        display: flex;
        gap: 0 1em;

        dt {
          font-weight: 700;
        }
      }
    }
  }
}

.okk-store-anchor {
  margin-top: rem(44);
  display: flex;
  justify-content: center;
  align-items: center;

  a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    @include fz(16, 24);
    padding: rem(22) rem(30);
    border: 2px solid $COLOR_OKK_BASE;
    font-weight: 700;
    border-radius: rem(70);
    @extend .hover;
    text-align: center;

    span {
      padding: 0 rem(16);
      position: relative;

      &::after {
        position: absolute;
        content: "";
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        z-index: 1;
        height: rem(12.08);
        width: rem(8.43);
        background: url("../img/common/ico_down.png") no-repeat bottom center / cover;
      }
    }
  }

  @include sp {
    margin-top: rem(30);

    a {
      width: 100%;
      max-width: rem(335);
      padding: rem(12) rem(47);
      position: relative;

      span {
        position: static;

        &::after {
          right: rem(28);
          height: rem(15);
          width: rem(10.4677152318);
        }
      }
    }
  }
}

.okk-store-video {
  margin: rem(60) auto 0;
  max-width: rem(718);

  @include sp {
    margin-top: rem(50);
  }
}

.okk-store-google-map {
  &__iframe {
    overflow: hidden;
    aspect-ratio: 1156/592;

    @include sp {
      aspect-ratio: 335/268;
    }

    iframe {
      width: 100%;
      height: 100%;
    }

    a {
      width: 100%;
      height: 100%;
      display: block;
      @extend .hover;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.okk-store-google-map-search {
  margin-top: rem(80);
  text-align: center;

  &__box {
    margin-top: rem(40);
    background: #fff;
    border-radius: rem(20);
    padding: rem(40);

    .box-lead {
      line-height: 1.875;
      font-weight: 500;
    }

    .box-copy {
      display: flex;
      align-items: center;
      border: 1px solid #dedede;
      overflow: hidden;
      border-radius: rem(12);
      background: #fff;
      width: rem(518);
      margin: rem(30) auto 0;

      &__text {
        @include fz(24, 35);
        font-weight: 700;
        text-align: center;
        flex-grow: 1;
      }

      &__btn {
        width: rem(82);
        flex-shrink: 0;
        background: $COLOR_OKK_GRAY_LIGHT;
        border: none;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        height: rem(80);
        transition: $TRANSITION;

        img {
          width: rem(24);
          height: rem(28.5);
        }

        .copy-status {
          display: none;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 1;
          display: none;
          width: rem(40);
          height: rem(40);
          background: $COLOR_OKK_GRAY_LIGHT;

          img {
            width: 100%;
            height: 100%;
          }
        }

        @include pc {
          &:hover {
            opacity: $OPACITY_HOVER;
          }
        }
      }
    }
  }

  @include sp {
    margin-top: rem(50);

    &__box {
      margin-top: rem(30);
      border-radius: rem(12);
      padding: rem(22) rem(20);

      .box-lead {
        text-align: left;
      }

      .box-copy {
        border-radius: rem(10);
        margin: rem(20) auto 0;
        width: 100%;
        max-width: rem(300);

        &__text {
          @include fz(14, 20);
        }

        &__btn {
          width: rem(45);
          height: rem(45);

          img {
            width: rem(13.8);
            height: rem(16.39);
          }

          .copy-status {
            width: rem(22);
            height: rem(22);
          }
        }
      }
    }
  }
}

.okk-store-map-access {
  padding: rem(60) 0 rem(80);

  .okk-headline {
    text-align: center;
    margin-bottom: rem(30);
  }

  &__content {
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(40);
    border-radius: rem(20);
  }

  &__ttl {
    @include fz(32, 46);
    font-weight: 700;
    text-align: center;
    margin-bottom: rem(35);
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: rem(14) rem(40);

    dl {
      width: calc(50% - 2rem);
      border-radius: rem(12);
      background: #fff;
      padding: rem(20);

      dt {
        @include fz(24, 35);
        font-weight: 700;
        color: $COLOR_OKK_RED;
        margin-bottom: rem(10);
      }

      dd {
        font-weight: 500;
        line-height: 1.75;

        li+li {
          margin-top: rem(2);
        }
      }
    }
  }

  &__note {
    margin-top: rem(20);

    li {
      line-height: 1.875;
      font-weight: 500;
    }
  }

  @include sp {
    padding: rem(50) 0 rem(60);

    .okk-headline {
      margin-bottom: rem(20);
    }

    &__content {
      padding: rem(22) rem(20);
      border-radius: rem(12);
    }

    &__ttl {
      @include fz(18, 28);
      margin-bottom: rem(14);
    }

    &__list {
      gap: rem(8);

      dl {
        width: 100%;
        border-radius: rem(10);
        padding: rem(12) rem(20);

        dt {
          @include fz(16, 30);
          margin-bottom: rem(6);
        }

        dd {
          font-size: rem(12);

          li+li {
            margin-top: rem(1);
          }
        }
      }
    }

    &__note {
      margin-top: rem(12);

      li {
        font-size: rem(12);
      }
    }
  }
}

.okk-cm-service {
  .okk-section-head+.okk-ls-services {
    margin-top: 0;
  }

  &.okk-bg-gray {
    .okk-ls-services {
      background: #fff;

      &__item__inner {
        background: $COLOR_OKK_GRAY_LIGHT;
      }
    }
  }
}

.okk-anchor {
  padding: rem(30) 0 rem(60);

  &__box {
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(30) rem(40) rem(40);
    border-radius: rem(12);
  }

  &__list {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: rem(14) rem(20);
  }

  &__btn {
    height: 100%;
    background: #fff;
    border-radius: rem(12);
    display: flex;
    align-items: center;
    color: $COLOR_OKK_RED;
    @include fz(18, 26);
    font-weight: 700;
    padding: rem(16) rem(30) rem(16) rem(20);
    position: relative;
    @extend .hover;

    &::after {
      position: absolute;
      content: "";
      top: 50%;
      right: rem(16);
      transform: translateY(-50%);
      width: rem(11);
      height: rem(14);
      z-index: 1;
      background: url("../img/common/ico_down_red.png") no-repeat bottom center / cover;
    }
  }

  @include sp {
    padding: rem(20) 0 rem(40);

    &__box {
      padding: rem(20) rem(20) rem(22);
    }

    &__list {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: rem(10) rem(9);
    }

    &__btn {
      border-radius: rem(10);
      @include fz(12, 18);
      padding: rem(10) rem(16) rem(10) rem(10);

      &::after {
        right: rem(8);
        width: rem(8);
        height: rem(10);
      }
    }
  }

  @include tablet {
    &__list {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
}

.okk-anchor02 {
  &__list {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: rem(15) rem(26);
  }

  &__btn {
    height: 100%;
    background: #fff;
    border-radius: rem(12);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(3);
    color: inherit;
    @include fz(20, 29);
    font-weight: 700;
    padding: rem(12) rem(20) rem(22);
    position: relative;
    @extend .hover;

    &::after {
      position: absolute;
      content: "";
      bottom: rem(10);
      left: 50%;
      transform: translateX(-50%);
      width: rem(13);
      height: rem(7.5);
      z-index: 1;
      background: url("../img/common/ico_arrow_returnplan.png") no-repeat bottom center / cover;
    }

    .ico {
      width: rem(44);
    }
  }

  @include sp {
    &__list {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: rem(15);
    }

    &__btn {
      border-radius: rem(10);
      gap: rem(4);
      @include fz(16, 24);
      padding: rem(10) rem(20) rem(20);

      .ico {
        width: rem(36);
      }
    }
  }

  @include tablet {
    &__list {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
}

.okk-faq {
  &__item {
    background: #fff;
    box-shadow: $SHADOW_BASE;
    border-radius: rem(12);

    &:nth-child(n + 2) {
      margin-top: rem(16);
    }
  }

  &__question {
    cursor: pointer;
    padding: rem(24) rem(82) rem(24) rem(40);
    font-weight: 700;
    @include fz(18, 26);
    color: $COLOR_OKK_RED;
    position: relative;

    &::before {
      position: absolute;
      content: "";
      top: 50%;
      transform: translateY(-50%);
      right: rem(30);
      width: rem(32);
      height: rem(32);
      z-index: 1;
      border: 1px solid $COLOR_OKK_BASE;
      border-radius: 50%;
      transition: $TRANSITION;
    }

    .txt {

      &::before,
      &::after {
        position: absolute;
        content: "";
        top: 50%;
        transform: translateY(-50%);
        right: rem(41);
        width: rem(10);
        height: 1px;
        background: $COLOR_OKK_BASE;
        border-radius: rem(10);
        transition: $TRANSITION;
      }

      &::after {
        transform: translateY(-50%) rotate(90deg);
      }
    }

    &.is-active {
      .txt {
        &::after {
          opacity: 0 !important;
        }
      }
    }

    @include hover {
      &:hover {

        &::before,
        .txt::before,
        .txt::after {
          opacity: $OPACITY_HOVER;
        }
      }
    }
  }

  &__answer {
    border-top: 1px solid $COLOR_OKK_BORDER;
    padding: rem(22) rem(40);
    @include fz(16, 32);
    font-weight: 500;

    .txt {
      a {
        text-decoration: underline;
        text-underline-offset: 2px;
        transition: $TRANSITION;

        @include pc {
          &:hover {
            opacity: 0.75;
            text-decoration: none;
          }
        }
      }
    }

    .faq-txt-btn {
      padding: rem(12) rem(20);
      text-decoration: none !important;
      margin-top: rem(10);

      @include pc {
        max-width: rem(250);
      }
    }
  }

  @include sp {
    &__item {
      &:nth-child(n + 2) {
        margin-top: rem(10);
      }
    }

    &__question {
      padding: rem(14) rem(66) rem(14) rem(20);
      @include fz(16, 26);
      min-height: rem(60);
      display: flex;
      align-items: center;

      &::before {
        right: rem(14);
      }

      .txt {

        &::before,
        &::after {
          right: rem(25);
        }
      }
    }

    &__answer {
      padding: rem(20);
      @include fz(14, 26);
    }
  }

  .okk-home & {
    &__question {
      cursor: pointer;
      padding: 1.8rem 7rem 1.8rem 2rem;
      @include fz(16, 28);

      &::before {
        right: 2rem;
      }

      .txt {

        &::before,
        &::after {
          right: 3.1rem;
        }
      }
    }

    &__answer {
      border-top-color: #e0e0e0;
      padding: 2rem;
      @include fz(14, 24);
    }

    @media(min-width:768px) {
      &__items {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
      }

      &__item:nth-child(n+2) {
        margin-top: 2rem;
      }

      &__items_col {
        width: calc(50% - 1rem);
      }
    }

    @media(max-width:767px) {
      &__items_col:nth-child(n+2) {
        margin-top: 1rem;
      }

      &__question {
        min-height: unset;
      }
    }
  }
}

.okk-about {
  &-point {
    margin-top: rem(40);
    background: $COLOR_OKK_RED_LIGHT;
    padding: rem(40) 0 rem(50);

    &__list {
      display: grid;
      gap: rem(40) rem(38);
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    &__item {
      height: 100%;
      background: #fff;
      border-radius: rem(25) rem(25) rem(12) rem(12);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    &__img {
      flex-shrink: 0;
      aspect-ratio: 360/206;
      @extend .okk-img-fit;
    }

    &__desc {
      flex-grow: 1;
      padding: rem(17) rem(10) rem(25);
      @include fz(18, 26);
      font-weight: 700;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    @include sp {
      margin-top: rem(10);
      padding: rem(30) 0 rem(40);

      &__list {
        gap: rem(10) rem(11);
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }

      &__item {
        border-radius: rem(12);
      }

      &__img {
        aspect-ratio: 162/93;
      }

      &__desc {
        padding: rem(12) rem(10);
        @include fz(14, 20);
      }
    }

    @include tablet {
      &__list {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
    }
  }

  &-history {
    &__item {
      display: flex;
      flex-direction: column;
      gap: rem(20);

      &:nth-child(n + 2) {
        margin-top: rem(40);
      }
    }

    &__img {}

    .figure {
      border-radius: rem(12);
      aspect-ratio: 335/200;
      @extend .okk-img-fit;

      &--sp {
        margin-top: rem(40);
      }
    }

    &__desc {}

    &__ttl {
      color: $COLOR_OKK_BLUE;
      font-weight: 700;
      @include fz(18, 28);
      margin-bottom: rem(20);
    }

    &__copy {
      p {
        &:nth-of-type(n + 2) {
          margin-top: rem(20);
        }
      }
    }

    @include pc {
      &__item {
        flex-direction: row;
        gap: rem(40);

        &:nth-child(n + 2) {
          margin-top: rem(40);
        }

        &:nth-child(even) {
          flex-direction: row-reverse;
        }
      }

      &__img {
        width: rem(558);
        max-width: calc(50% - 2rem);
        flex-shrink: 0;
      }

      .figure {
        border-radius: rem(25);
        aspect-ratio: 558/400;

        &:nth-of-type(n + 2) {
          margin-top: rem(20);
        }
      }

      &__desc {
        flex-grow: 1;
      }

      &__ttl {
        @include fz(24, 38);
        margin-bottom: rem(40);
      }

      &__copy {
        p {
          &:nth-of-type(n + 2) {
            margin-top: rem(40);
          }
        }
      }
    }
  }

  &-feature {
    &__list {
      display: flex;
      flex-direction: column;
      gap: rem(30);
    }

    &__item {
      background: #fff;
      padding: rem(20) rem(20) rem(30);
      border-radius: rem(12);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: rem(20);
    }

    &__img {
      aspect-ratio: 295/200;
      border-radius: rem(10);
      @extend .okk-img-fit;
    }

    &__desc {}

    &__ttl {
      color: $COLOR_OKK_RED;
      @include fz(18, 28);
      font-weight: 700;
      margin-bottom: rem(20);
    }

    &__copy {}

    @include pc {
      &__list {
        gap: rem(40);
      }

      &__item {
        padding: rem(40);
        border-radius: rem(20);
        align-items: flex-start;
        flex-direction: row;
        gap: rem(40);

        &:nth-child(even) {
          flex-direction: row-reverse;
        }
      }

      &__img {
        aspect-ratio: 538/451;
        border-radius: rem(25);
        flex-shrink: 0;
        width: 50%;
      }

      &__desc {
        flex-grow: 1;
      }

      &__ttl {
        @include fz(24, 38);
        margin-bottom: rem(30);
      }

      &__copy {}
    }
  }

  &-thoughts {
    &__inner {
      position: relative;
    }

    &__content {
      padding: rem(60) rem(280);
      position: relative;
      z-index: 2;

      .okk-copy {
        text-align: center;
      }
    }

    &__img {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
      display: flex;
      justify-content: space-between;
      list-style: none;

      li {
        width: rem(264);
        height: 100%;
        overflow: hidden;
        position: relative;

        span {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-position: center;
          background-repeat: repeat-y;
          background-size: 100% rem(1254);
          z-index: 1;
        }

        &:nth-child(1) {
          span {
            animation: 25s linear infinite scrollAboutDown;
            background-image: url("../img/about/thoughts_img01.png");
          }
        }

        &:nth-child(2) {
          span {
            animation: 25s linear infinite scrollAboutUp;
            background-image: url("../img/about/thoughts_img02.png");
          }
        }
      }
    }

    @include sp {
      padding: rem(60) 0;

      &__content {
        padding: rem(163) 0;

        .okk-section-head {
          margin-bottom: rem(20);
        }

        .okk-copy {
          text-align: left;
        }
      }

      &__img {
        flex-direction: column;
        margin: 0 rem(-20);
        width: calc(100% + #{rem(40)});

        li {
          width: 100%;
          height: rem(133);

          span {
            background-repeat: repeat-x;
            background-size: rem(459) 100%;
          }

          &:nth-child(1) {
            span {
              animation: 25s linear infinite scrollAboutRight;
              background-image: url("../img/about/thoughts_img01_sp.png");
            }
          }

          &:nth-child(2) {
            span {
              animation: 25s linear infinite scrollAboutLeft;
              background-image: url("../img/about/thoughts_img02_sp.png");
            }
          }
        }
      }
    }
  }

  &-greeting {
    padding-bottom: rem(10);

    &__list {
      display: flex;
      flex-direction: column;
      gap: rem(20);
    }

    &__item {
      background: $COLOR_OKK_RED_LIGHT;
      padding: rem(20) rem(20) rem(30);
      border-radius: rem(12);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: rem(20);
    }

    &__img {
      aspect-ratio: 295/200;
      border-radius: rem(10);
      @extend .okk-img-fit;
    }

    &__name {
      margin-top: rem(16);
      color: $COLOR_OKK_RED;
      @include fz(18, 28);
      font-weight: 700;
    }

    @include pc {
      padding-bottom: rem(30);

      &__list {
        gap: rem(30);
      }

      &__item {
        padding: rem(40);
        border-radius: rem(20);
        align-items: center;
        flex-direction: row;
        gap: rem(40);
      }

      &__img {
        aspect-ratio: 518/400;
        border-radius: rem(25);
        flex-shrink: 0;
        width: 50%;
      }

      &__desc {
        flex-grow: 1;
      }

      &__name {
        margin-top: rem(20);
        @include fz(20, 28);
      }
    }
  }
}

.okk-flow {
  padding: rem(25) 0 rem(60);
  background: $COLOR_OKK_BLUE_LIGHT;

  &__items {
    padding-top: rem(30);
    display: flex;
    flex-direction: column;
    gap: rem(50);
  }

  &__item {
    position: relative;
  }

  &__num {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    border: 2px solid $COLOR_OKK_RED;
    border-radius: 50%;
    width: rem(60);
    height: rem(60);
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    @include fz(20, 30);
    font-weight: 700;
    color: $COLOR_OKK_RED;
  }

  &__content {
    border-radius: rem(12);
    background: #fff;
    padding: rem(44) rem(20) rem(30);

    @include sp {
      display: flex;
      flex-direction: column;
    }
  }

  &__ttl {
    @include fz(18, 26);
    font-weight: 700;
    color: $COLOR_OKK_RED;
    margin-bottom: rem(10);

    @include sp {
      order: -1;
      text-align: center;
    }
  }

  &__img {
    margin-bottom: rem(16);

    figure {
      overflow: hidden;
      border-radius: rem(10);

      @include sp {
        aspect-ratio: 295/200;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  &__desc {
    dt {
      margin-bottom: rem(8);
      @include fz(16, 24);
      font-weight: 700;
    }

    @include sp {
      dl+.okk-flow__img {
        margin-top: rem(30);
      }
    }
  }

  &__btn {
    margin-top: rem(20);

    a+a {
      margin-top: rem(10);
    }
  }

  @include pc {
    padding: rem(40) 0 rem(80);

    &__items {
      padding-top: 0;
      gap: rem(30);
      overflow: hidden;
    }

    &__item {
      display: flex;
      align-items: flex-start;
      gap: rem(40);

      &:not(:last-child) {
        position: relative;

        &::before {
          content: "";
          position: absolute;
          top: rem(60);
          left: rem(57);
          width: 3px;
          height: 100%;
          background: $COLOR_OKK_RED;
          z-index: 1;
        }
      }
    }

    &__num {
      position: relative;
      top: 0;
      left: 0;
      flex-shrink: 0;
      transform: translate(0, 0);
      border-width: 3px;
      width: rem(117);
      height: rem(117);
      @include fz(38, 57);
    }

    &__content {
      flex-grow: 1;
      border-radius: rem(20);
      padding: rem(40);
      overflow: hidden;
    }

    &__ttl {
      margin-top: rem(20);
      @include fz(24, 35);
      margin-bottom: rem(26);
    }

    &__img {
      float: left;
      margin-right: rem(40);
      margin-bottom: 0;
      width: rem(380);

      figure {
        border-radius: rem(12);

        &:nth-child(n + 2) {
          margin-top: rem(20);
        }
      }
    }

    &__desc {
      padding-left: rem(420);

      dl:nth-of-type(n + 2) {
        margin-top: rem(30);
      }

      dt {
        @include fz(20, 30);
      }
    }

    &__btn {
      margin-top: rem(30);

      a+a {
        margin-top: rem(14);
      }
    }
  }

  @include tablet() {
    &__item {
      &:not(:last-child) {
        &::before {
          top: rem(60);
          left: rem(45);
        }
      }
    }

    &__num {
      width: rem(93);
      height: rem(93);
      @include fz(32);
    }

    &__content {
      flex-grow: 1;
      border-radius: rem(20);
      padding: rem(40);
      display: flex;
      flex-direction: column;
    }

    &__ttl {
      margin-top: 0;
      margin-bottom: rem(20);
      order: -1;
    }

    &__img {
      float: none;
      margin-right: 0;
      margin-bottom: rem(20);
      width: rem(380);
    }

    &__desc {
      padding-left: 0;
    }

    &__btn {
      margin-top: rem(20);
    }
  }
}

.okk-returnplan {
  padding: rem(40) 0 rem(60);
  background: $COLOR_OKK_BLUE_LIGHT;

  &__items {
    display: flex;
    flex-direction: column;
    gap: rem(30);
  }

  &__item {
    border-radius: rem(12);
    background: #fff;
    padding: rem(20) rem(20) rem(30);
  }

  &__heading-row {
    display: flex;
    flex-direction: column;
    gap: rem(10);
    margin-bottom: rem(16);

    .okk-heading-lv3 {
      margin-bottom: 0;
    }
  }

  &__img {
    margin-bottom: rem(16);

    figure {
      overflow: hidden;
      border-radius: rem(10);
      position: relative;

      figcaption {
        position: absolute;
        bottom: rem(10);
        left: 0;
        left: 50%;
        transform: translateX(-50%);
        padding: rem(6) rem(14);
        background: #fff;
        @include fz(12, 17);
        font-weight: 500;
        border-radius: rem(40);
        text-align: center;
        width: max-content;
        max-width: 90%;
      }
    }
  }

  &__desc {
    display: flex;
    flex-direction: column;
    gap: rem(16);

    @include sp {
      .okk-list-dot {
        font-size: rem(12);

        li {
          padding-left: rem(16);
        }
      }
    }

    .okk-note {
      margin-top: rem(16);
    }
  }

  @include tabletUp() {
    padding: rem(60) 0 rem(80);

    &__item {
      border-radius: rem(20);
      padding: rem(40);
    }

    &__heading-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: rem(35);
      margin-bottom: rem(30);
    }

    &__row {
      display: flex;
      align-items: flex-start;
      gap: rem(40);
    }

    &__img {
      margin-bottom: 0;
      width: rem(538);
      flex-shrink: 0;

      figure {
        border-radius: rem(12);

        figcaption {
          bottom: rem(20);
          padding: rem(7) rem(14);
          @include fz(16, 24);
          border-radius: rem(80);
        }
      }
    }

    &__desc {
      flex-grow: 1;
      flex-direction: column-reverse;
      gap: rem(20);
    }
  }
}

.okk-luggage-storage {
  padding: rem(40) 0 rem(60);

  &__box {
    border-radius: rem(12);
    background: $COLOR_OKK_GRAY_LIGHT;
    padding: rem(20) rem(20) rem(30);
    display: flex;
    flex-direction: column;
    gap: rem(20);
  }

  &__content {
    @include sp {
      .okk-list-dot {
        font-size: rem(12);

        li {
          padding-left: rem(16);
        }
      }
    }
  }

  &__content-item {
    &:nth-child(n + 2) {
      margin-top: rem(24);
    }
  }

  @include tabletUp() {
    padding: rem(60) 0 rem(80);

    &__box {
      border-radius: rem(20);
      background: $COLOR_OKK_GRAY_LIGHT;
      padding: rem(40) rem(40) rem(50);
      gap: rem(30);
    }

    &__il {
      max-width: rem(846);
      margin: 0 auto;
    }

    &__content-item {
      &:nth-child(n + 2) {
        margin-top: rem(30);
      }
    }
  }
}

.okk-course {
  padding-bottom: rem(30);

  &__content {
    max-width: rem(945);
    margin: 0 auto;
  }

  &__item {
    background: #fff;
    box-shadow: $SHADOW_BASE;
    border-radius: rem(12);

    &:nth-child(n + 2) {
      margin-top: rem(14);
    }
  }

  &__heading {
    cursor: pointer;
    padding: rem(15) rem(82) rem(15) rem(40);
    font-weight: 700;
    @include fz(20, 30);
    color: $COLOR_OKK_RED;
    position: relative;

    &::before {
      position: absolute;
      content: "";
      top: 50%;
      transform: translateY(-50%);
      right: rem(30);
      width: rem(32);
      height: rem(32);
      z-index: 1;
      border: 1px solid $COLOR_OKK_BASE;
      border-radius: 50%;
      transition: $TRANSITION;
    }

    .txt {
      display: flex;
      gap: rem(28);

      &::before,
      &::after {
        position: absolute;
        content: "";
        top: 50%;
        transform: translateY(-50%);
        right: rem(41);
        width: rem(10);
        height: 1px;
        background: $COLOR_OKK_BASE;
        border-radius: rem(10);
        transition: $TRANSITION;
      }

      &::after {
        transform: translateY(-50%) rotate(90deg);
      }

      .case {
        color: $COLOR_OKK_BASE;
        @include fz(16, 30);
      }
    }

    &.is-active {
      .txt {
        &::after {
          opacity: 0 !important;
        }
      }
    }

    @include hover {
      &:hover {

        &::before,
        .txt::before,
        .txt::after {
          opacity: $OPACITY_HOVER;
        }
      }
    }
  }

  &__body {
    padding: rem(20) rem(40) rem(26) rem(26);
  }

  .case-schedule {
    display: flex;
    align-items: flex-start;

    &:not(:first-child) {
      margin-top: rem(30);
    }

    &__time {
      width: rem(82);
      flex-shrink: 0;
      @include fz(16, 26);
      font-weight: 700;
      padding-right: rem(5);
    }

    &__content {
      flex-grow: 1;

      ul+p,
      p+ul {
        margin-top: 1em;
      }
    }

    &__title {
      margin-bottom: rem(20);
      @include fz(18, 26);
      font-weight: 700;
    }

    &__image-text {
      display: flex;
      align-items: start;
      gap: rem(18);

      &+.case-schedule__copy {
        margin-top: 1em;
      }
    }

    &__image {
      width: rem(292);
      flex-shrink: 0;
      border-radius: rem(30);
      overflow: hidden;
    }

    &__text {
      flex-grow: 1;
    }

    &__copy {
      line-height: 1.625;
    }

    &__list-dot {
      li {
        position: relative;
        padding-left: 1.1em;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          content: "・";
        }

        &:nth-child(n+2) {
          margin-top: rem(5);
        }

        b {
          margin-right: 0.5em;
        }
      }
    }
  }

  &__sttl {
    @include fz(26, 37);
    font-weight: 700;
    margin-bottom: rem(14);
  }

  &__btn {
    margin-top: rem(66);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  @include sp {
    margin-bottom: rem(30);

    &__item {
      &:nth-child(n + 2) {
        margin-top: rem(10);
      }
    }

    &__heading {
      padding: rem(14) rem(66) rem(14) rem(20);
      @include fz(16);

      &::before {
        right: rem(14);
      }

      .txt {
        flex-direction: column;
        gap: rem(6);

        &::before,
        &::after {
          right: rem(25);
        }

        .case {
          @include fz(14);
        }
      }
    }

    &__body {
      padding: rem(20) rem(20) rem(26);
    }

    .case-schedule {
      flex-direction: column;
      gap: rem(10);

      &__time {
        width: 100%;
        @include fz(14);
      }

      &__title {
        @include fz(16);
      }

      &__image-text {
        flex-direction: column;
        gap: rem(16);
      }

      &__image {
        width: rem(292);
        border-radius: rem(20);
      }
    }

    &__sttl {
      @include fz(20);
      margin-bottom: rem(10);
    }

    &__btn {
      margin-top: rem(35);
    }
  }
}

.okk-link-seo {
  padding-bottom: rem(86);

  &__content {
    max-width: rem(945);
    margin: 0 auto;
    background: $COLOR_OKK_GRAY_LIGHT;
    border-radius: rem(20);
    padding: rem(40);
  }

  &__title {
    @include fz(18, 26);
    font-weight: 700;
    margin-bottom: rem(15);

    &:nth-of-type(n+2) {
      margin-top: rem(30);
    }
  }

  &__list {
    max-width: rem(800);
    display: flex;
    flex-wrap: wrap;
    gap: rem(5) 3em;
    @include fz(14, 26);
    font-weight: 500;
    overflow: hidden;

    li {
      position: relative;

      &::before {
        position: absolute;
        top: 50%;
        left: -1.5em;
        content: "｜";
        z-index: 1;
        transform: translate(-50%, -50%);
      }
    }
  }

  a {
    transition: $TRANSITION;
    text-decoration: underline;
    text-underline-offset: 2px;

    @include hover {
      &:hover {
        text-decoration: none;
      }
    }
  }

  @include sp {
    padding-top: rem(30);
    padding-bottom: rem(50);

    &__content {
      border-radius: rem(12);
      padding: rem(20);
      gap: rem(20);
    }

    &__title {
      @include fz(16);
      margin-bottom: rem(12);
    }

    &__list {
      gap: rem(5) 3em;
    }
  }

  @include sm {
    &__list {
      @include fz(12);
      flex-direction: column;
    }
  }
}

// Responsive Fix
@include laptop() {
  .okk-t-links {
    padding-bottom: rem(20);
  }

  .okk-t-rentalplan__item {
    padding: rem(10) rem(6) rem(6) rem(19);
  }

  .okk-ls-services {
    padding: rem(20);

    &__item__inner {
      padding-right: rem(15);
    }

    &__desc {
      padding-left: rem(15);
    }
  }

  .okk-slider-gallery__photo .slider-item {
    width: rem(300);
  }

  .okk-t-returnplan,
  .okk-t-hairset {
    &__desc {
      padding: rem(20);
    }

    &__row {
      gap: rem(20);
    }
  }

  .okk-t-corporate {
    &__content {
      padding: rem(20);
    }

    &__item {
      max-width: calc(50% - #{rem(10)});
    }
  }

  .okk-t-column__list,
  .okk-t-publication__list,
  .okk-t-corporate__list,
  .okk-footer-fc {
    gap: rem(20);
  }
}

@include tablet() {
  .okk-header {
    &-top {
      &__row {
        flex-direction: column;
      }
    }

    &__site {
      padding-top: rem(10);
      padding-right: 0;
      padding-bottom: 0;
    }

    &__group-btn {
      padding-top: rem(10);
      padding-left: calc(100% - #{rem(247)});
    }

    .okk-box-lang {
      top: rem(12);
    }

    &-main__row {
      flex-direction: column;
      align-items: center;
      gap: rem(20);
    }

    &-nav {
      max-width: 100%;

      &__col {
        padding: 0 1.5%;

        dl {
          max-width: 100%;
        }
      }
    }
  }

  .okk-header-nav-pc {
    display: none;
  }

  .okk-btn-wrap {
    gap: rem(14);
  }

  .okk-t-kv {
    &__title {
      font-size: rem(36);
    }

    &__slogan {
      bottom: rem(40);
    }

    .slide-item {
      aspect-ratio: 1366/1100;
    }
  }

  .okk-t-links {
    margin-top: 0;
    padding-bottom: 0;
  }

  .okk-video-popup {
    &__content {
      padding: rem(5);
      border-radius: rem(12);
    }

    .close-btn {
      right: 0;
      top: rem(-40);
      color: #fff;
    }
  }

  .okk-t-rentalplan {
    &__item {
      width: rem(320);
    }

    &__link .rentalplan-ttl {
      font-size: rem(30);
    }
  }

  .okk-t-hairset {
    &__item {
      width: calc(50% - #{rem(10)});
    }
  }

  .okk-t-returnplan__item {
    width: calc(50% - #{rem(10)});
  }

  .okk-t-about {
    &__img li {
      width: rem(132);

      span {
        background-size: 100% rem(626.5);
      }
    }

    &__content {
      padding: rem(40) rem(15);
    }
  }

  .okk-about-thoughts {
    &__img li {
      width: rem(132);

      span {
        background-size: 100% rem(627);
      }
    }

    &__content {
      padding: rem(40) rem(15);
    }
  }

  .okk-ls-services__item {
    width: calc(50% - #{rem(10)});
  }

  .okk-t-news .news-time {
    width: rem(130);
  }

  .okk-footer-language li {
    padding: 0 rem(7);
  }

  .okk-map-modal {
    width: rem(600);
    padding: rem(25);
    border-radius: rem(15);

    &__ttl {
      font-size: rem(32);
      line-height: 1.40625;
      margin-bottom: rem(15);
    }

    &__photo {
      border-radius: rem(20);
    }

    &__btn {
      margin-top: rem(20);

      a {
        width: rem(300);
      }
    }

    &__copy {
      margin-top: rem(15);
      font-size: 14px;
    }

    &__slider {
      .slick-arrow {
        width: rem(56);
        height: rem(56);
        border-width: 1px;
        margin-top: rem(30);

        &.slick-prev {
          left: rem(-90);
        }

        &.slick-next {
          right: rem(-90);
        }
      }
    }

    .slider-counter {
      bottom: rem(-42);
      font-size: rem(18);
    }

    .mfp-close {
      top: rem(-25);
      right: rem(-60);
    }
  }
}

// Reserve
.okk-reserve-store {
  padding: rem(60) 0 rem(90);

  &__list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: rem(30);
  }

  &__item {
    width: calc((100% - #{rem(90)}) / 4);
    box-shadow: $SHADOW_BASE;
    border-radius: rem(12);
    background: #fff;
    overflow: hidden;

    @include pc {
      min-width: rem(266);
    }
  }

  &__btn {
    display: flex;
    height: 100%;
    background: #fff;
    @extend .hover;
    flex-direction: column;
  }

  &__img {
    flex-shrink: 0;
    aspect-ratio: 266/160;
    @extend .okk-img-fit;
    position: relative;

    &::after {
      position: absolute;
      content: "";
      z-index: 1;
      width: rem(40);
      height: rem(40);
      bottom: rem(10);
      right: rem(10);
      background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center / rem(12.08) rem(8.43);
      border: 1px solid $COLOR_OKK_BASE;
      border-radius: 50%;
    }
  }

  &__ttl {
    flex-grow: 1;
    padding: rem(20) rem(10);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    @include fz(14, 24);
    font-weight: 700;

    .fz-lg {
      @include fz(20, 28);
      color: $COLOR_OKK_RED;
    }
  }

  @include sp {
    padding: rem(30) 0 rem(40);

    &__list {
      gap: rem(16);
      justify-content: flex-start;
    }

    &__item {
      width: calc(50% - #{rem(8)});
    }

    &__img {
      aspect-ratio: 160/80;

      &::after {
        width: rem(32);
        height: rem(32);
        bottom: rem(5);
        right: rem(8);
        background-size: rem(10) rem(6.978476821);
      }
    }

    &__ttl {
      padding: rem(12) rem(10);
      @include fz(12, 18);

      .fz-lg {
        @include fz(16, 22);
      }
    }
  }
}

.okk-reserve-step {
  position: relative;
  z-index: 1;

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: rem(50);
    justify-content: center;
  }

  &__item {
    position: relative;

    &:not(:first-child) {
      &::before {
        content: "";
        position: absolute;
        top: rem(40);
        right: 50%;
        width: calc(100% + #{rem(30)});
        height: 3px;
        background: $COLOR_OKK_GRAY_LIGHT;
        z-index: -1;
      }
    }

    &.is-current {
      &::before {
        background: $COLOR_OKK_RED;
      }
    }

    .ico {
      width: rem(80);
      flex-shrink: 0;
    }

    .txt {
      @include fz(14, 32);
      font-weight: 500;
      text-align: center;
      margin-top: rem(10);
    }
  }

  @include sp {
    &__list {
      gap: rem(16);
    }

    &__item {
      &:not(:first-child) {
        &::before {
          top: rem(23);
        }
      }

      .ico {
        width: rem(46);
      }

      .txt {
        @include fz(10, 13);
        margin-top: rem(4);
        display: flex;
        min-height: rem(28);
        flex-direction: column;
        justify-content: center;
      }
    }

  }

}

.okk-reserve-unit {
  .unit {
    padding: rem(60) rem(40);
    background-color: #fff;
    border-radius: rem(12);

    &_select {
      display: flex;
      align-items: center;
      gap: rem(10);
    }
  }

  .filter {
    &_select {
      &_item {
        position: relative;
        width: rem(140);
        font-size: rem(20);
        line-height: 1.5;
      }

      .select-selected {
        display: flex;
        align-items: center;
        height: 100%;
        border: 1px solid #707070;
        border-radius: rem(8);
        min-height: rem(58);

        &::after {
          width: rem(10);
          height: rem(5);
          background: url(../img/common/ico_arrow01.svg) no-repeat center center;
          background-size: 100% 100%;
        }

        &.select-arrow-active::after {
          transform: scale(-1);
        }
      }

      .select-items {
        position: absolute;
        background-color: #fff;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 99;
        border: 1px solid #707070;
        border-radius: rem(8);
      }
    }


  }

  .sel-visittime_note {
    @include fz(16, 32);
    margin-top: rem(20);

    a {
      color: $COLOR_OKK_RED;
      text-decoration: underline;
    }
  }


  .cm-month {
    position: relative;
    width: 100%;

    .item {
      width: rem(528);
      margin-right: rem(20);
      background-color: #fff;

      &:last-child {
        margin-right: 0;
      }

      &_month {
        color: $COLOR_OKK_RED;
        @include fz(24, 43);
        font-weight: bold;
        text-align: center;
        margin-bottom: rem(16);
      }

      .cm-tbl {
        width: 100%;
        border-collapse: collapse;
        font-size: rem(16);
        color: #000;
        line-height: 1.875;
        text-align: center;
        font-weight: bold;
        background-color: $COLOR_OKK_GRAY_LIGHT;


        td {
          border: 1px solid #E2E2E2;
        }

        th {
          padding: rem(1) 0;
          text-align: center;
          font-weight: bold;
          width: 14.2857143%;
          background-color: $COLOR_OKK_RED;
          color: #fff;
          @include fz(16, 32);
        }

        tbody {
          tr {
            &:last-child {
              td {
                border-bottom: none;
              }
            }
          }
        }

        td {
          width: 14.2857143%;
          @include fz(16, 32);

          &:first-child {
            border-left: none;
          }

          &:last-child {
            border-right: none;
          }

          a {
            padding: 1rem 0;
            color: inherit;
            display: block;
            outline: none;
            transition: all 0.5s ease;

            &:hover {
              background-color: #f7c5c5;
            }

            &.day_active {
              color: $COLOR_OKK_BASE;
              background-color: #FFFABF;
            }
          }

          &.c-sun {
            color: $COLOR_OKK_RED;
            background-color: $COLOR_OKK_RED_LIGHT;
          }

          &.c-sat {
            color: $COLOR_OKK_BLUE;
            background-color: $COLOR_OKK_BLUE_LIGHT;
          }

          &.off {
            a {
              opacity: 0;
            }
          }
        }

        .c-disable {
          color: #ccc !important;
          background-color: #efefef !important;

          a {
            pointer-events: none;
            cursor: default;
          }
        }
      }
    }

    .slick-prev,
    .slick-next {
      width: rem(45);
      height: rem(45);
      background: url(../img/common/ico_arrow02.png) no-repeat center center #fff;
      background-size: rem(45);
      top: 0;
      z-index: 10;
      border-radius: 50%;

      &::before {
        content: none;
      }
    }

    .slick-prev {
      background-image: url(../img/common/ico_arrow02_left.png);
      right: rem(50);
      left: auto;
    }

    .slick-next {
      background-image: url(../img/common/ico_arrow02.png);
      right: 0;
    }

    .slick-disabled {
      visibility: hidden;
    }
  }

  .sel-visittime {
    margin-top: rem(56);

    &_note {
      @include fz(16, 32);
      font-weight: 500;
      margin-bottom: 2.5rem;
      text-align: left;
    }

    .item {
      &_month {
        background-color: $COLOR_OKK_RED;
        color: #fff;
        font-weight: bold;
        @include fz(24, 43);
        text-align: center;
        padding: rem(4) rem(10);
      }

      .cm-tbl {
        width: 100%;
        border-collapse: collapse;
        @include fz(16, 32);
        color: #bc0e1d;
        text-align: center;
        font-weight: bold;
        background-color: #fff;

        th,
        td {
          border: 1px solid #e6e6e6;
        }

        th {
          padding: rem(3) 0;
          text-align: center;
          font-weight: bold;
          color: #000;

          &.c-sun {
            color: $COLOR_OKK_RED;
            background-color: $COLOR_OKK_RED_LIGHT;
          }

          &.c-sat {
            color: $COLOR_OKK_BLUE;
            background-color: $COLOR_OKK_BLUE_LIGHT;
          }
        }

        td {
          padding: rem(3) 0;
          font-family: "Yu Gothic", YuGothic, "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "メイリオ", Meiryo, "MS Pゴシック", "MS PGothic", sans-serif;
          cursor: pointer;

          &.wait_cancel {
            background-color: $COLOR_OKK_GRAY_LIGHT;

            a {
              color: inherit;
              border-bottom: 1px solid $COLOR_OKK_RED;
              outline: none;
            }
          }

          &.time_check {
            background-color: #FDFABF;
          }
        }

        .c-time {
          width: rem(75);
          padding: rem(10) rem(15);
        }

        .space {
          width: 0;
          border: none;
          padding: 0;
          display: none;
        }
      }
    }

    .slick-prev,
    .slick-next {
      width: rem(45);
      height: rem(45);
      background: url(../img/common/ico_arrow02.png) no-repeat center center #fff;
      background-size: rem(45);
      top: rem(-35);
      z-index: 10;
      border-radius: 50%;

      &::before {
        content: none;
      }
    }

    .slick-prev {
      background-image: url(../img/common/ico_arrow02_left.png);
      right: rem(50);
      left: auto;
    }

    .slick-next {
      background-image: url(../img/common/ico_arrow02.png);
      right: 0;
    }

    .slick-disabled {
      visibility: hidden;
    }

    @include sp {
      margin-top: 5.6rem;

      &_note {
        font-size: 1.4rem;
        line-height: 1.875;
        color: #202020;
        margin-bottom: 2rem;
      }

      .item {
        overflow: hidden;

        &_inner {
          overflow-x: scroll;
          padding-bottom: 1rem;

          &::-webkit-scrollbar {
            height: 0.8rem;
          }

          &::-webkit-scrollbar-track {
            background: #ececec;
          }

          &::-webkit-scrollbar-thumb {
            background: $COLOR_OKK_RED;
          }
        }

        &_month {
          background-color: $COLOR_OKK_RED;
          color: #fff;
          font-weight: bold;
          font-size: 1.4rem;
          text-align: center;
          line-height: 1.875;
          padding: 0.5rem 1rem;
        }

        .cm-tbl {
          width: 100%;
          border-collapse: collapse;
          font-size: 1.4rem;
          color: $COLOR_OKK_RED;
          line-height: 1.875;
          text-align: center;
          font-weight: bold;
          background-color: #fff;
          min-width: 65rem;

          th,
          td {
            border: 1px solid #e6e6e6;
          }

          th {
            padding: 0.6rem 0.2rem;
            text-align: center;
            font-weight: bold;
            color: #000;

            &.c-sun {
              color: $COLOR_OKK_RED;
              background-color: #fff5f6;
            }

            &.c-sat {
              color: #0f50fb;
              background-color: #ecf8ff;
            }
          }

          td {
            padding: 0.6rem 0;

            &.wait_cancel {
              background-color: #efefef;

              a {
                color: inherit;
                border-bottom: 1px solid #c96469;
                outline: none;
              }
            }

            &.time_check {
              background-color: $COLOR_OKK_RED;
              color: #fff;
            }
          }

          tr {
            position: relative;
          }

          .c-time {
            width: 7.5rem;
            padding: 0.6rem 1.5rem;
            position: sticky;
            top: 0;
            left: 0;
            background: #fff;
            z-index: 10;
          }

          .space {
            width: 7.3rem;
            padding: 0.6rem 1.5rem;
          }
        }
      }
    }
  }

  .reserveIndexForm-back {
    margin-top: rem(50);
    text-align: center;

    a {
      color: $COLOR_OKK_BASE;
      font-weight: bold;
      @include fz(16, 32);
      text-decoration: underline;
      padding-left: rem(20);
      position: relative;
      text-underline-offset: 2px;

      &::after {
        position: absolute;
        content: "";
        z-index: 1;
        background: url("../img/common/ico_arrow_left.png") no-repeat right center / cover;
        width: rem(12);
        height: rem(8);
        top: 55%;
        left: 0;
        transform: translateY(-50%);
      }
    }
  }

  .cm-form_ttl {
    @include fz(32, 57);
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: $COLOR_OKK_RED;
  }

  .reserveForm {
    padding-bottom: 12rem;
    margin-top: 9rem;

    &_ttl {
      margin-bottom: 2.5rem;
      text-align: center;
    }

    &-copy {
      font-size: 1.6rem;
      line-height: 1.875;
      text-align: center;
      margin-bottom: 4rem;
    }

    &-cpl {
      font-size: 2.2rem;
      line-height: 1.875;
      margin-bottom: 4rem;
      text-align: center;
    }

    &-form {

      &_item {
        margin-bottom: 4rem;
        font-size: 1.6rem;
        display: flex;
        align-items: flex-start;

        .form-lbl {
          width: 240/1156*100%;
          padding-right: 1.5rem;
          padding-top: 1.3rem;
          font-weight: bold;
          text-align: left;

          p {
            color: $COLOR_OKK_BASE;

            .c-red {
              background: $COLOR_OKK_RED;
              color: #fff !important;
              border-radius: 2px;
              @include fz(12, 24);
              letter-spacing: 0.01em;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              width: rem(40);
              margin-right: rem(6);
            }

            .notice {
              color: #9d9d9d;
              font-weight: normal;
            }
          }
        }

        .form-cnt {
          display: block;
          width: 900/1156*100%;

          input,
          textarea {
            width: 100%;
            padding: rem(17) rem(20);
            border: 1px solid #707070;
            outline: none;
            @include fz(16, 32);
            font-weight: 500;
            border-radius: 8px;
            background-color: transparent;

            &::placeholder {
              color: #B1B1B1;
              font-size: inherit;
              font-weight: inherit
            }
          }

          .notice {
            @include fz(12, 24);
            margin-top: rem(10);
            text-align: left;
          }
        }
      }

      &_information {
        background-color: #fff;
        padding: rem(10) rem(20) rem(20);
        color: #202020;
        margin-bottom: rem(40);

        .ttl {
          margin-bottom: 1rem;
          font-size: 1.6rem;
          line-height: 1.5;
          font-weight: bold;
        }

        .copy {
          font-size: 1.4rem;
          font-weight: 300;

          a {
            color: $COLOR_OKK_RED;
            border-bottom: 1px solid $COLOR_OKK_RED;
          }
        }
      }
    }

    [class*="mdl-btn-"] {
      margin: 0 2.8rem;
      color: #fff;
      background-color: $COLOR_OKK_RED;
      box-shadow: none;
    }
  }

  @media print,
  screen and (max-width: 1040px) and (min-width: 900px) {
    .cm-month {
      margin-left: -1rem;
      margin-right: -1rem;

      .item {
        width: calc(50vw - 3rem);
        margin: 0 1rem;
      }

      .slick-prev {
        right: 6rem;
        left: auto;
      }

      .slick-next {
        right: 1rem;
      }
    }
  }

  #form-modal {
    width: rem(1156);
    max-width: calc(100vw - 4rem);
    background-color: #fff;
    padding: rem(40) 0;
    margin: 0 auto;
    box-shadow: 0.7rem 0.7rem 3rem 0 rgba(0, 0, 0, 0.3);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: -1;
    opacity: 0;
    transition: z-index 0s, opacity 0.5s, transform 0.3s;
    transform: translate(-50%, -0%);
    height: 90vh;
    border-radius: 12px;

    &.visible {
      transform: translate(-50%, -50%);
      opacity: 1;
      z-index: 2001;
    }

    .form-modal_inner {
      padding: 0 rem(40);
      overflow-y: scroll;
      overflow-x: hidden;
      height: 100%;

      &::-webkit-scrollbar {
        width: rem(3);
      }

      &::-webkit-scrollbar-track {
        background: #ccc;
      }

      &::-webkit-scrollbar-thumb {
        background: $COLOR_OKK_RED;
        cursor: pointer;
      }
    }

    .closeBtn {
      position: absolute;
      top: rem(20);
      right: rem(20);
      cursor: pointer;
    }

    .reserveForm {
      margin-top: rem(10);
      padding-bottom: rem(20);
    }
  }
}
(function ($) {
  "use strict";
  let isNavigating = false;

  function setFillHeightVh() {
    const setVh = function () {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };
    $(window).on("load resize", setVh);
  }

  function anchorLink() {
    $("html").on("click", 'a.js-anc-btn[href^="#"]', function (e) {
      e.preventDefault();
      var href = $(this).attr("href");
      var target = $(href === "#" || href === "" ? "html" : href);
      var headerHeight = $(".okk-header").innerHeight();
      var position = target.offset().top - headerHeight + 2;

      $("html, body").animate(
        {
          scrollTop: position,
        },
        "swing"
      );
    });

    $(".header-nav a, .footer-nav a").on("click", function (e) {
      if ($(".p-top").length > 0) {
        e.preventDefault();

        var href = $(this).attr("href");
        var targetId = href.split("#")[1];

        var $target = $("#" + targetId);
        if ($target.length > 0) {
          var headerHeight = $(".header").innerHeight();
          isNavigating = true;
          $("html, body").animate(
            {
              scrollTop: $target.offset().top - headerHeight + 2,
            },
            300
          );
        }
      }
    });
  }

  function anchorLinkHash() {
    const setLinkHash = function () {
      if (isNavigating) return;
      const hash = window.location.hash;
      const anchorTarget = $(hash);
      if (anchorTarget.length) {
        setTimeout(function () {
          const headerHeight = $(".header").innerHeight();
          const position = anchorTarget.offset().top - headerHeight + 2;
          $("html, body").animate(
            {
              scrollTop: position,
            },
            550,
            "swing"
          );
          history.pushState(null, null, hash);
        }, 200);
      }
    };
    $(window).on("load resize", setLinkHash);
  }

  function initModal() {
    let current = "";

    function openModal(_id) {
      current = _id;
      $(current).addClass("is-show");
      $("body").addClass("is-modal-open");
    }

    function closeModal() {
      $(current).removeClass("is-show");
      $("body").removeClass("is-modal-open");
    }

    $(".js-modal-btn").on("click", function (e) {
      openModal($(this).data("modal"));
      e.preventDefault();
    });

    $(".js-modal-close").on("click", function () {
      closeModal();
    });

    $(".js-modal-overlay").on("click", function (e) {
      if ($(e.target).hasClass("js-modal-overlay")) {
        closeModal();
      }
    });

    $(document).on("keydown", function (e) {
      if (e.key === "Escape" || e.key === "Esc") {
        closeModal();
      }
    });
  }

  function initTabs() {
    $(".js-tabs-content").removeClass("is-current");

    $(".js-tabs-btn").click(function () {
      const $tabsWrap = $(this).closest(".js-tabs-wrap");
      $tabsWrap.find(".js-tabs-btn").removeClass("is-current");
      $(this).addClass("is-current");
      const tabName = $(this).data("tabs");
      $tabsWrap.find(".js-tabs-content").removeClass("is-current");
      $("#" + tabName).addClass("is-current");
    });

    $(".js-tabs-btn.is-current").trigger("click");
  }

  function menuHeader() {
    const $header = $(".okk-header");
    const $hamburgerBtn = $(".okk-header-hamburger");
    const $headerLinks = $(".okk-header-nav a");
    const $headerTopWrap = $(".okk-header-top__wrap");
    const $headerMain = $(".okk-header-main");

    if ($hamburgerBtn) {
      $hamburgerBtn.on("click", function () {
        // $("body").toggleClass("no-scroll");
        $header.toggleClass("is-open");
      });
    }
    $headerLinks.on("click", function () {
      // $("body").removeClass("no-scroll");
      $header.removeClass("is-open");
    });

    $(document).click(function (e) {
      if (!$(e.target).closest(".okk-header").length) {
        $header.removeClass("is-open");
      }
    });

    $(window).on("load resize", function () {
      const headerTopWrapHeight = $headerTopWrap.outerHeight();
      $headerMain.css("paddingTop", headerTopWrapHeight);
    });
  }

  function setPaddingTopWrapperRenewal() {
    var setPaddingTop = function () {
      var $header = $("#okk-header");
      var $wrapper = $("#wrapper");

      if ($header.length) {
        var heightHeader = $header.innerHeight();
        $wrapper.css("paddingTop", heightHeader);
      }
    };

    $(window).on("load scroll resize", setPaddingTop);
  }

  function selectLanguage() {
    const $languageLabel = $(".okk-box-lang__label");
    const $languageList = $(".okk-box-lang__list");
    const $languageItem = $languageList.find("a");

    $languageLabel.click(function () {
      $languageList.slideToggle("fast");
    });

    $languageItem.click(function () {
      $languageList.slideUp("fast");
    });

    $(document).click(function (e) {
      if (!$(e.target).closest(".okk-box-lang").length) {
        $languageList.slideUp("fast");
      }
    });
  }

  function kvTopSlider() {
    if ($(".js-t-kv-slider").length > 0) {
      var $slide = $(".js-t-kv-slider")
        .slick({
          autoplay: true,
          autoplaySpeed: 8500,
          arrows: false,
          dots: true,
          draggable: false,
          fade: true,
          infinite: true,
          pauseOnFocus: false,
          pauseOnHover: false,
          pauseOnDotsHover: false,
          slidesToShow: 1,
          slidesToScroll: 1,
          speed: 3000,
        })
        .on({
          beforeChange: function (event, slick, currentSlide, nextSlide) {
            $(".slick-slide", this).eq(currentSlide).addClass("preve-slide");
            $(".slick-slide", this).eq(nextSlide).addClass("slide-animation");
            if (nextSlide > 0) {
              $slide.slick("slickSetOption", "autoplaySpeed", 5500, true);
              $(".slick-dots li").append(
                '<svg class="c-indicator__circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52" width="52" height="52"><circle class="svg-elem-1" stroke="#fff" stroke-width="2" cx="26" cy="26" r="25"></circle></svg>'
              );
            }
          },
          afterChange: function () {
            $(".preve-slide", this).removeClass("preve-slide slide-animation");
          },
        });
      $slide.find(".slick-slide").eq(0).addClass("slide-animation");
      $(".slick-dots li").append(
        '<svg class="c-indicator__circle" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52" width="52" height="52"><circle class="svg-elem-1" stroke="#fff" stroke-width="2" cx="26" cy="26" r="25"></circle></svg>'
      );
    }
  }

  function gallerySlider() {
    if ($(".js-gallery-slider").length > 0) {
      $(".js-gallery-slider").slick({
        dots: false,
        infinite: true,
        arrows: false,
        autoplay: true,
        speed: 5000,
        autoplaySpeed: 0,
        slidesToShow: 1,
        centerMode: true,
        variableWidth: true,
        pauseOnHover: false,
        cssEase: "linear",
      });
    }
  }

  function scrollbarSlider() {
    $(".js-slider-scrollbar").each(function () {
      var $this = $(this);
      var $slider = $this.find(".js-slider-scrollbar-el");
      var $progressBar = $this.find(".js-slider-scrollbar-progress__bar");

      $slider.on("init", function (event, slick) {
        var initialProgress = (1 / slick.slideCount) * 100;
        $progressBar
          .css("width", initialProgress + "%")
          .text(initialProgress.toFixed(2) + "% completed");
      });

      $slider.on(
        "beforeChange",
        function (event, slick, currentSlide, nextSlide) {
          var progressBarWidth = ((nextSlide + 1) / slick.slideCount) * 100;
          $progressBar
            .css("width", progressBarWidth + "%")
            .text(progressBarWidth.toFixed(2) + "% completed");
        }
      );

      $slider.slick({
        variableWidth: true,
        slidesToScroll: 1,
        infinite: true,
        speed: 400,
        arrows: true,
        dots: false,
        touchThreshold: 20,
      });
    });
  }

  function initSlider($slider, itemCount, slidesToShow) {
    if (!$slider.hasClass("slick-initialized")) {
      $slider.slick({
        slidesToShow: slidesToShow,
        slidesToScroll: 1,
        arrows: true,
        variableWidth: slidesToShow === 1,
        centerMode: slidesToShow === 1,
      });
    }
  }

  function destroySlider($slider) {
    if ($slider.hasClass("slick-initialized")) {
      $slider.slick("unslick");
    }
  }

  function updateSlider($slider, itemCount, windowWidth) {
    if (windowWidth > 768 && itemCount > 3) {
      initSlider($slider, itemCount, 3);
    } else if (windowWidth <= 767 && itemCount > 1) {
      initSlider($slider, itemCount, 1);
    } else {
      destroySlider($slider);
    }
  }

  function topLinksSlider() {
    const $slider = $(".js-t-links-slider");
    const itemCount = $slider.children(".okk-t-links__item").length;

    function resizeHandler() {
      const windowWidth = $(window).width();
      updateSlider($slider, itemCount, windowWidth);
    }

    resizeHandler();
    $(window).on("resize", resizeHandler);
  }

  function topStoreOtherSlider() {
    const $slider = $(".js-store-other-slider");
    const itemCount = $slider.children(".okk-store-other__item").length;

    function resizeHandler() {
      const windowWidth = $(window).width();
      updateSlider($slider, itemCount, windowWidth);
    }

    resizeHandler();
    $(window).on("resize", resizeHandler);
  }

  function initAccordion() {
    var _scope_ = null;
    var btnEl = [".js-accordion-btn"];

    btnEl.forEach(function (element) {
      $(element, _scope_).each(function () {
        if (!$(this).hasClass("is-active")) {
          $(this).next().hide().toggleClass("is-active");
        }
        $(this).click(function () {
          $(this).toggleClass("is-active");
          $(this)
            .next()
            .slideToggle("fast", function () {
              $(this).toggleClass("is-active");
            });
        });
      });
    });
  }

  function pageScroll() {
    const $pageTopTrigger = $(".js-pagetop");
    const $header = $(".okk-header");
    const $footer = $(".okk-footer");
    const scrollTarget = $(window).height();
    const headerHeight = $header.height();
    // const footerTop = $footer.offset().top;
    const footerHeight = $footer.height();

    $(window).on("load scroll", function () {
      const scrollTop = $(window).scrollTop();

      if (scrollTop > headerHeight) {
        $header.addClass("is-fixed");
      } else {
        $header.removeClass("is-fixed");
      }

      if (scrollTop > scrollTarget) {
        $pageTopTrigger.addClass("is-active");
      } else {
        $pageTopTrigger.removeClass("is-active");
      }

      // if (footerTop <= scrollTop + scrollTarget) {
      //   const newBottom = footerHeight - $pageTopTrigger.outerHeight() / 2;
      //   $pageTopTrigger.addClass("is-absolute").css({
      //     bottom: newBottom,
      //   });
      // } else {
      //   $pageTopTrigger.removeClass("is-absolute").css({
      //     bottom: "2rem",
      //   });
      // }
    });

    $pageTopTrigger.on("click", function () {
      $("body,html").animate({ scrollTop: 0 }, 300);
    });
  }

  function videoYoutubePopup() {
    $(".js-video-youtube-btn").on("click", function () {
      const videoId = $(this).data("video");
      const videoUrl =
        "https://www.youtube.com/embed/" +
        videoId +
        "?autoplay=1&rel=0&loop=1&playlist=" +
        videoId;

      $("#youtubeVideo").attr("src", videoUrl);
      $("#videoPopup").addClass("is-active");
    });

    function closeVideoPopup() {
      $("#videoPopup").removeClass("is-active");
      $("#youtubeVideo").attr("src", "");
    }

    $("#closePopup").on("click", function () {
      closeVideoPopup();
    });

    $(window).on("click", function (e) {
      if ($(e.target).is("#videoPopup")) {
        closeVideoPopup();
      }
    });

    $(document).on("keydown", function (e) {
      if (e.key === "Escape") {
        closeVideoPopup();
      }
    });
  }

  function elMatchHeight() {
    $(".js-el-matchHeight").matchHeight();
    $(".js-el-matchHeight-01").matchHeight();
    $(".js-el-matchHeight-02").matchHeight();
    $(".js-el-matchHeight-03").matchHeight();
  }

  function storeSlider() {
    if ($(".js-store-slider").length > 0) {
      $(".js-store-slider").slick({
        dots: false,
        arrows: true,
        infinite: true,
        autoplay: true,
        speed: 500,
        slidesToShow: 1,
        centerMode: true,
        variableWidth: true,
        pauseOnHover: false,
        touchThreshold: 20,
      });
    }
  }

  function storeMapSlider() {
    const $slider = $(".js-store-map-slider");
    if ($slider.length > 0 && !$slider.hasClass("slick-initialized")) {
      $slider.slick({
        dots: false,
        arrows: true,
        infinite: true,
        autoplay: false,
        fade: true,
        speed: 300,
        slidesToShow: 1,
        slidesToScroll: 1,
        pauseOnHover: false,
        touchThreshold: 20,
      });
      const $sliderCounter = $(
        '<div class="slider-counter">1　/　' +
          $slider.slick("getSlick").slideCount +
          "</div>"
      );
      $slider.after($sliderCounter);

      $slider.on("afterChange", function (event, slick, currentSlide) {
        const currentIndex = currentSlide + 1;
        const totalSlides = slick.slideCount;
        $sliderCounter.text(currentIndex + "　/　" + totalSlides);
      });
    }
  }

  function lockBodyScroll() {
    const scrollTop = $(window).scrollTop();
    $("body").css("top", `-${scrollTop}px`).addClass("body-no-scroll");
  }

  function unlockBodyScroll() {
    const scrollTop = parseInt($("body").css("top"), 10) * -1;
    $("body").removeClass("body-no-scroll").css("top", "");
    $(window).scrollTop(scrollTop);
  }

  function initMapModal() {
    if ($(".js-modal-btn").length > 0) {
      $(".js-modal-btn").magnificPopup({
        type: "inline",
        removalDelay: 250,
        mainClass: "mfp-zoom",
        callbacks: {
          open: function () {
            storeMapSlider();
            setTimeout(function () {
              $(".js-store-map-slider").slick("setPosition");
            }, 0);
          },
          close: function () {
            // $(".js-store-map-slider").slick("unslick");
          },
        },
      });
    }
  }

  function copyTextFn() {
    $("#js-copy-btn").on("click", function () {
      var copyText = $("#js-copy-text").text();

      var $tempInput = $("<input>");
      $("body").append($tempInput);
      $tempInput.val(copyText).select();

      try {
        document.execCommand("copy");
        $("#js-copy-status").show();
        setTimeout(function () {
          $("#js-copy-status").fadeOut();
        }, 1000);
      } catch (err) {
        console.log("Failed to copy text: ", err);
      }

      $tempInput.remove();
    });
  }
  function all_init() {
    if ($(".cm-month").length > 0) {
      $(".cm-month").show();
      $(".cm-month").slick({
        dots: false,
        arrows: true,
        speed: 700,
        autoplay: false,
        infinite: false,
        autoplaySpeed: 2000,
        variableWidth: true,
        swipe: false,
        slidesToShow: 2,
        slidesToScroll: 2,
        responsive: [
          {
            breakpoint: 1040,
            settings: {
              variableWidth: false,
            },
          },
          {
            breakpoint: 900,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              variableWidth: false,
            },
          },
          {
            breakpoint: 767,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              variableWidth: false,
            },
          },
        ],
      });
    }
    $(".sel-visittime").show();
    selVisittimeSlider();
  }
  var selVisittimeSlider = function () {
    if ($(".sel-visittime .items").length > 0) {
      $(".sel-visittime .items").show();
      $(".sel-visittime .items").slick({
        dots: false,
        arrows: true,
        speed: 500,
        autoplay: false,
        infinite: false,
        autoplaySpeed: 2000,
        variableWidth: false,
        swipe: false,
        slidesToShow: 1,
        slidesToScroll: 1,
      });
    }
  };

  function reset_times_events() {
    $(".sel-visittime .item .cm-tbl td:not(.wait_cancel)").off("click");
    $(".sel-visittime .item .cm-tbl td:not(.wait_cancel)").click(function () {
      $(".time_check").removeClass("time_check");
      var $this = $(this);
      var ymd = $this.attr("__ymd");
      var time = $this.attr("__time");
      var $td = $("[__ymd=" + ymd + '][__time="' + time + '"]');
      $td.addClass("time_check");
      $("[name=date_reserve]").val(ymd);
      $("[name=time_reserve]").val(time);
      reflesh_plans(true, true);
      reflesh_primary();
    });
    $(".wait_cancel").off("click");
    $(".wait_cancel").click(function () {
      $("#form-modal").attr("_cancel_date", $(this).attr("__ymd"));
      $("#form-modal").attr("_cancel_time", $(this).attr("__time"));
    });
    $(".wait_cancel").on("click", function (e) {
      e.preventDefault();
      $("#wrapper").addClass("formModal-visible");
      $("#form-modal").addClass("visible");
      $("#form-modal .closeBtn, .popup-overlay").on("click", function () {
        $("#wrapper").removeClass("formModal-visible");
        $("#form-modal").removeClass("visible");
      });
    });
    /*if (!$('.sel-visittime .items').hasClass('slick-slider')) {
			selVisittimeSlider();
		}*/
  }
  function scrollable() {
    $(".sel-visittime .item").each(function () {
      let $this = $(this);
      let $slider = $this.find(".js-scrollable-el");
      new ScrollHint($slider);
    });
  }
  //--- init
  //------------
  $(function () {
    setFillHeightVh();
    menuHeader();
    setPaddingTopWrapperRenewal();
    selectLanguage();
    kvTopSlider();
    gallerySlider();
    scrollbarSlider();
    topLinksSlider();
    topStoreOtherSlider();
    elMatchHeight();
    initAccordion();
    pageScroll();
    videoYoutubePopup();
    storeSlider();
    initMapModal();
    copyTextFn();
    // initModal();
    // initTabs();
    // anchorLinkHash();
    // anchorLink();
    all_init();
    reset_times_events();
    scrollable();
  });
})(jQuery);

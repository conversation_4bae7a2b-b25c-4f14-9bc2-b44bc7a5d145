@charset "UTF-8";
@media screen and (min-width: 768px) {
  .sp {
    display: none !important;
  }
}
@media screen and (max-width: 767px) {
  .pc {
    display: none !important;
  }
}
.okk-mt0 {
  margin-top: 0rem !important;
}

.okk-mr0 {
  margin-right: 0rem !important;
}

.okk-mb0 {
  margin-bottom: 0rem !important;
}

.okk-ml0 {
  margin-left: 0rem !important;
}

.okk-pt0 {
  padding-top: 0rem !important;
}

.okk-pr0 {
  padding-right: 0rem !important;
}

.okk-pb0 {
  padding-bottom: 0rem !important;
}

.okk-pl0 {
  padding-left: 0rem !important;
}

.okk-mt5 {
  margin-top: 0.5rem !important;
}

.okk-mr5 {
  margin-right: 0.5rem !important;
}

.okk-mb5 {
  margin-bottom: 0.5rem !important;
}

.okk-ml5 {
  margin-left: 0.5rem !important;
}

.okk-pt5 {
  padding-top: 0.5rem !important;
}

.okk-pr5 {
  padding-right: 0.5rem !important;
}

.okk-pb5 {
  padding-bottom: 0.5rem !important;
}

.okk-pl5 {
  padding-left: 0.5rem !important;
}

.okk-mt10 {
  margin-top: 1rem !important;
}

.okk-mr10 {
  margin-right: 1rem !important;
}

.okk-mb10 {
  margin-bottom: 1rem !important;
}

.okk-ml10 {
  margin-left: 1rem !important;
}

.okk-pt10 {
  padding-top: 1rem !important;
}

.okk-pr10 {
  padding-right: 1rem !important;
}

.okk-pb10 {
  padding-bottom: 1rem !important;
}

.okk-pl10 {
  padding-left: 1rem !important;
}

.okk-mt15 {
  margin-top: 1.5rem !important;
}

.okk-mr15 {
  margin-right: 1.5rem !important;
}

.okk-mb15 {
  margin-bottom: 1.5rem !important;
}

.okk-ml15 {
  margin-left: 1.5rem !important;
}

.okk-pt15 {
  padding-top: 1.5rem !important;
}

.okk-pr15 {
  padding-right: 1.5rem !important;
}

.okk-pb15 {
  padding-bottom: 1.5rem !important;
}

.okk-pl15 {
  padding-left: 1.5rem !important;
}

.okk-mt20 {
  margin-top: 2rem !important;
}

.okk-mr20 {
  margin-right: 2rem !important;
}

.okk-mb20 {
  margin-bottom: 2rem !important;
}

.okk-ml20 {
  margin-left: 2rem !important;
}

.okk-pt20 {
  padding-top: 2rem !important;
}

.okk-pr20 {
  padding-right: 2rem !important;
}

.okk-pb20 {
  padding-bottom: 2rem !important;
}

.okk-pl20 {
  padding-left: 2rem !important;
}

.okk-mt25 {
  margin-top: 2.5rem !important;
}

.okk-mr25 {
  margin-right: 2.5rem !important;
}

.okk-mb25 {
  margin-bottom: 2.5rem !important;
}

.okk-ml25 {
  margin-left: 2.5rem !important;
}

.okk-pt25 {
  padding-top: 2.5rem !important;
}

.okk-pr25 {
  padding-right: 2.5rem !important;
}

.okk-pb25 {
  padding-bottom: 2.5rem !important;
}

.okk-pl25 {
  padding-left: 2.5rem !important;
}

.okk-mt30 {
  margin-top: 3rem !important;
}

.okk-mr30 {
  margin-right: 3rem !important;
}

.okk-mb30 {
  margin-bottom: 3rem !important;
}

.okk-ml30 {
  margin-left: 3rem !important;
}

.okk-pt30 {
  padding-top: 3rem !important;
}

.okk-pr30 {
  padding-right: 3rem !important;
}

.okk-pb30 {
  padding-bottom: 3rem !important;
}

.okk-pl30 {
  padding-left: 3rem !important;
}

.okk-mt35 {
  margin-top: 3.5rem !important;
}

.okk-mr35 {
  margin-right: 3.5rem !important;
}

.okk-mb35 {
  margin-bottom: 3.5rem !important;
}

.okk-ml35 {
  margin-left: 3.5rem !important;
}

.okk-pt35 {
  padding-top: 3.5rem !important;
}

.okk-pr35 {
  padding-right: 3.5rem !important;
}

.okk-pb35 {
  padding-bottom: 3.5rem !important;
}

.okk-pl35 {
  padding-left: 3.5rem !important;
}

.okk-mt40 {
  margin-top: 4rem !important;
}

.okk-mr40 {
  margin-right: 4rem !important;
}

.okk-mb40 {
  margin-bottom: 4rem !important;
}

.okk-ml40 {
  margin-left: 4rem !important;
}

.okk-pt40 {
  padding-top: 4rem !important;
}

.okk-pr40 {
  padding-right: 4rem !important;
}

.okk-pb40 {
  padding-bottom: 4rem !important;
}

.okk-pl40 {
  padding-left: 4rem !important;
}

.okk-mt45 {
  margin-top: 4.5rem !important;
}

.okk-mr45 {
  margin-right: 4.5rem !important;
}

.okk-mb45 {
  margin-bottom: 4.5rem !important;
}

.okk-ml45 {
  margin-left: 4.5rem !important;
}

.okk-pt45 {
  padding-top: 4.5rem !important;
}

.okk-pr45 {
  padding-right: 4.5rem !important;
}

.okk-pb45 {
  padding-bottom: 4.5rem !important;
}

.okk-pl45 {
  padding-left: 4.5rem !important;
}

.okk-mt50 {
  margin-top: 5rem !important;
}

.okk-mr50 {
  margin-right: 5rem !important;
}

.okk-mb50 {
  margin-bottom: 5rem !important;
}

.okk-ml50 {
  margin-left: 5rem !important;
}

.okk-pt50 {
  padding-top: 5rem !important;
}

.okk-pr50 {
  padding-right: 5rem !important;
}

.okk-pb50 {
  padding-bottom: 5rem !important;
}

.okk-pl50 {
  padding-left: 5rem !important;
}

.okk-mt55 {
  margin-top: 5.5rem !important;
}

.okk-mr55 {
  margin-right: 5.5rem !important;
}

.okk-mb55 {
  margin-bottom: 5.5rem !important;
}

.okk-ml55 {
  margin-left: 5.5rem !important;
}

.okk-pt55 {
  padding-top: 5.5rem !important;
}

.okk-pr55 {
  padding-right: 5.5rem !important;
}

.okk-pb55 {
  padding-bottom: 5.5rem !important;
}

.okk-pl55 {
  padding-left: 5.5rem !important;
}

.okk-mt60 {
  margin-top: 6rem !important;
}

.okk-mr60 {
  margin-right: 6rem !important;
}

.okk-mb60 {
  margin-bottom: 6rem !important;
}

.okk-ml60 {
  margin-left: 6rem !important;
}

.okk-pt60 {
  padding-top: 6rem !important;
}

.okk-pr60 {
  padding-right: 6rem !important;
}

.okk-pb60 {
  padding-bottom: 6rem !important;
}

.okk-pl60 {
  padding-left: 6rem !important;
}

.okk-mt65 {
  margin-top: 6.5rem !important;
}

.okk-mr65 {
  margin-right: 6.5rem !important;
}

.okk-mb65 {
  margin-bottom: 6.5rem !important;
}

.okk-ml65 {
  margin-left: 6.5rem !important;
}

.okk-pt65 {
  padding-top: 6.5rem !important;
}

.okk-pr65 {
  padding-right: 6.5rem !important;
}

.okk-pb65 {
  padding-bottom: 6.5rem !important;
}

.okk-pl65 {
  padding-left: 6.5rem !important;
}

.okk-mt70 {
  margin-top: 7rem !important;
}

.okk-mr70 {
  margin-right: 7rem !important;
}

.okk-mb70 {
  margin-bottom: 7rem !important;
}

.okk-ml70 {
  margin-left: 7rem !important;
}

.okk-pt70 {
  padding-top: 7rem !important;
}

.okk-pr70 {
  padding-right: 7rem !important;
}

.okk-pb70 {
  padding-bottom: 7rem !important;
}

.okk-pl70 {
  padding-left: 7rem !important;
}

.okk-mt75 {
  margin-top: 7.5rem !important;
}

.okk-mr75 {
  margin-right: 7.5rem !important;
}

.okk-mb75 {
  margin-bottom: 7.5rem !important;
}

.okk-ml75 {
  margin-left: 7.5rem !important;
}

.okk-pt75 {
  padding-top: 7.5rem !important;
}

.okk-pr75 {
  padding-right: 7.5rem !important;
}

.okk-pb75 {
  padding-bottom: 7.5rem !important;
}

.okk-pl75 {
  padding-left: 7.5rem !important;
}

.okk-mt80 {
  margin-top: 8rem !important;
}

.okk-mr80 {
  margin-right: 8rem !important;
}

.okk-mb80 {
  margin-bottom: 8rem !important;
}

.okk-ml80 {
  margin-left: 8rem !important;
}

.okk-pt80 {
  padding-top: 8rem !important;
}

.okk-pr80 {
  padding-right: 8rem !important;
}

.okk-pb80 {
  padding-bottom: 8rem !important;
}

.okk-pl80 {
  padding-left: 8rem !important;
}

.okk-mt85 {
  margin-top: 8.5rem !important;
}

.okk-mr85 {
  margin-right: 8.5rem !important;
}

.okk-mb85 {
  margin-bottom: 8.5rem !important;
}

.okk-ml85 {
  margin-left: 8.5rem !important;
}

.okk-pt85 {
  padding-top: 8.5rem !important;
}

.okk-pr85 {
  padding-right: 8.5rem !important;
}

.okk-pb85 {
  padding-bottom: 8.5rem !important;
}

.okk-pl85 {
  padding-left: 8.5rem !important;
}

.okk-mt90 {
  margin-top: 9rem !important;
}

.okk-mr90 {
  margin-right: 9rem !important;
}

.okk-mb90 {
  margin-bottom: 9rem !important;
}

.okk-ml90 {
  margin-left: 9rem !important;
}

.okk-pt90 {
  padding-top: 9rem !important;
}

.okk-pr90 {
  padding-right: 9rem !important;
}

.okk-pb90 {
  padding-bottom: 9rem !important;
}

.okk-pl90 {
  padding-left: 9rem !important;
}

.okk-mt95 {
  margin-top: 9.5rem !important;
}

.okk-mr95 {
  margin-right: 9.5rem !important;
}

.okk-mb95 {
  margin-bottom: 9.5rem !important;
}

.okk-ml95 {
  margin-left: 9.5rem !important;
}

.okk-pt95 {
  padding-top: 9.5rem !important;
}

.okk-pr95 {
  padding-right: 9.5rem !important;
}

.okk-pb95 {
  padding-bottom: 9.5rem !important;
}

.okk-pl95 {
  padding-left: 9.5rem !important;
}

.okk-mt100 {
  margin-top: 10rem !important;
}

.okk-mr100 {
  margin-right: 10rem !important;
}

.okk-mb100 {
  margin-bottom: 10rem !important;
}

.okk-ml100 {
  margin-left: 10rem !important;
}

.okk-pt100 {
  padding-top: 10rem !important;
}

.okk-pr100 {
  padding-right: 10rem !important;
}

.okk-pb100 {
  padding-bottom: 10rem !important;
}

.okk-pl100 {
  padding-left: 10rem !important;
}

@media screen and (min-width: 768px) {
  .okk-pc-mt0 {
    margin-top: 0rem !important;
  }
  .okk-pc-mr0 {
    margin-right: 0rem !important;
  }
  .okk-pc-mb0 {
    margin-bottom: 0rem !important;
  }
  .okk-pc-ml0 {
    margin-left: 0rem !important;
  }
  .okk-pc-pt0 {
    padding-top: 0rem !important;
  }
  .okk-pc-pr0 {
    padding-right: 0rem !important;
  }
  .okk-pc-pb0 {
    padding-bottom: 0rem !important;
  }
  .okk-pc-pl0 {
    padding-left: 0rem !important;
  }
  .okk-pc-mt5 {
    margin-top: 0.5rem !important;
  }
  .okk-pc-mr5 {
    margin-right: 0.5rem !important;
  }
  .okk-pc-mb5 {
    margin-bottom: 0.5rem !important;
  }
  .okk-pc-ml5 {
    margin-left: 0.5rem !important;
  }
  .okk-pc-pt5 {
    padding-top: 0.5rem !important;
  }
  .okk-pc-pr5 {
    padding-right: 0.5rem !important;
  }
  .okk-pc-pb5 {
    padding-bottom: 0.5rem !important;
  }
  .okk-pc-pl5 {
    padding-left: 0.5rem !important;
  }
  .okk-pc-mt10 {
    margin-top: 1rem !important;
  }
  .okk-pc-mr10 {
    margin-right: 1rem !important;
  }
  .okk-pc-mb10 {
    margin-bottom: 1rem !important;
  }
  .okk-pc-ml10 {
    margin-left: 1rem !important;
  }
  .okk-pc-pt10 {
    padding-top: 1rem !important;
  }
  .okk-pc-pr10 {
    padding-right: 1rem !important;
  }
  .okk-pc-pb10 {
    padding-bottom: 1rem !important;
  }
  .okk-pc-pl10 {
    padding-left: 1rem !important;
  }
  .okk-pc-mt15 {
    margin-top: 1.5rem !important;
  }
  .okk-pc-mr15 {
    margin-right: 1.5rem !important;
  }
  .okk-pc-mb15 {
    margin-bottom: 1.5rem !important;
  }
  .okk-pc-ml15 {
    margin-left: 1.5rem !important;
  }
  .okk-pc-pt15 {
    padding-top: 1.5rem !important;
  }
  .okk-pc-pr15 {
    padding-right: 1.5rem !important;
  }
  .okk-pc-pb15 {
    padding-bottom: 1.5rem !important;
  }
  .okk-pc-pl15 {
    padding-left: 1.5rem !important;
  }
  .okk-pc-mt20 {
    margin-top: 2rem !important;
  }
  .okk-pc-mr20 {
    margin-right: 2rem !important;
  }
  .okk-pc-mb20 {
    margin-bottom: 2rem !important;
  }
  .okk-pc-ml20 {
    margin-left: 2rem !important;
  }
  .okk-pc-pt20 {
    padding-top: 2rem !important;
  }
  .okk-pc-pr20 {
    padding-right: 2rem !important;
  }
  .okk-pc-pb20 {
    padding-bottom: 2rem !important;
  }
  .okk-pc-pl20 {
    padding-left: 2rem !important;
  }
  .okk-pc-mt25 {
    margin-top: 2.5rem !important;
  }
  .okk-pc-mr25 {
    margin-right: 2.5rem !important;
  }
  .okk-pc-mb25 {
    margin-bottom: 2.5rem !important;
  }
  .okk-pc-ml25 {
    margin-left: 2.5rem !important;
  }
  .okk-pc-pt25 {
    padding-top: 2.5rem !important;
  }
  .okk-pc-pr25 {
    padding-right: 2.5rem !important;
  }
  .okk-pc-pb25 {
    padding-bottom: 2.5rem !important;
  }
  .okk-pc-pl25 {
    padding-left: 2.5rem !important;
  }
  .okk-pc-mt30 {
    margin-top: 3rem !important;
  }
  .okk-pc-mr30 {
    margin-right: 3rem !important;
  }
  .okk-pc-mb30 {
    margin-bottom: 3rem !important;
  }
  .okk-pc-ml30 {
    margin-left: 3rem !important;
  }
  .okk-pc-pt30 {
    padding-top: 3rem !important;
  }
  .okk-pc-pr30 {
    padding-right: 3rem !important;
  }
  .okk-pc-pb30 {
    padding-bottom: 3rem !important;
  }
  .okk-pc-pl30 {
    padding-left: 3rem !important;
  }
  .okk-pc-mt35 {
    margin-top: 3.5rem !important;
  }
  .okk-pc-mr35 {
    margin-right: 3.5rem !important;
  }
  .okk-pc-mb35 {
    margin-bottom: 3.5rem !important;
  }
  .okk-pc-ml35 {
    margin-left: 3.5rem !important;
  }
  .okk-pc-pt35 {
    padding-top: 3.5rem !important;
  }
  .okk-pc-pr35 {
    padding-right: 3.5rem !important;
  }
  .okk-pc-pb35 {
    padding-bottom: 3.5rem !important;
  }
  .okk-pc-pl35 {
    padding-left: 3.5rem !important;
  }
  .okk-pc-mt40 {
    margin-top: 4rem !important;
  }
  .okk-pc-mr40 {
    margin-right: 4rem !important;
  }
  .okk-pc-mb40 {
    margin-bottom: 4rem !important;
  }
  .okk-pc-ml40 {
    margin-left: 4rem !important;
  }
  .okk-pc-pt40 {
    padding-top: 4rem !important;
  }
  .okk-pc-pr40 {
    padding-right: 4rem !important;
  }
  .okk-pc-pb40 {
    padding-bottom: 4rem !important;
  }
  .okk-pc-pl40 {
    padding-left: 4rem !important;
  }
  .okk-pc-mt45 {
    margin-top: 4.5rem !important;
  }
  .okk-pc-mr45 {
    margin-right: 4.5rem !important;
  }
  .okk-pc-mb45 {
    margin-bottom: 4.5rem !important;
  }
  .okk-pc-ml45 {
    margin-left: 4.5rem !important;
  }
  .okk-pc-pt45 {
    padding-top: 4.5rem !important;
  }
  .okk-pc-pr45 {
    padding-right: 4.5rem !important;
  }
  .okk-pc-pb45 {
    padding-bottom: 4.5rem !important;
  }
  .okk-pc-pl45 {
    padding-left: 4.5rem !important;
  }
  .okk-pc-mt50 {
    margin-top: 5rem !important;
  }
  .okk-pc-mr50 {
    margin-right: 5rem !important;
  }
  .okk-pc-mb50 {
    margin-bottom: 5rem !important;
  }
  .okk-pc-ml50 {
    margin-left: 5rem !important;
  }
  .okk-pc-pt50 {
    padding-top: 5rem !important;
  }
  .okk-pc-pr50 {
    padding-right: 5rem !important;
  }
  .okk-pc-pb50 {
    padding-bottom: 5rem !important;
  }
  .okk-pc-pl50 {
    padding-left: 5rem !important;
  }
  .okk-pc-mt55 {
    margin-top: 5.5rem !important;
  }
  .okk-pc-mr55 {
    margin-right: 5.5rem !important;
  }
  .okk-pc-mb55 {
    margin-bottom: 5.5rem !important;
  }
  .okk-pc-ml55 {
    margin-left: 5.5rem !important;
  }
  .okk-pc-pt55 {
    padding-top: 5.5rem !important;
  }
  .okk-pc-pr55 {
    padding-right: 5.5rem !important;
  }
  .okk-pc-pb55 {
    padding-bottom: 5.5rem !important;
  }
  .okk-pc-pl55 {
    padding-left: 5.5rem !important;
  }
  .okk-pc-mt60 {
    margin-top: 6rem !important;
  }
  .okk-pc-mr60 {
    margin-right: 6rem !important;
  }
  .okk-pc-mb60 {
    margin-bottom: 6rem !important;
  }
  .okk-pc-ml60 {
    margin-left: 6rem !important;
  }
  .okk-pc-pt60 {
    padding-top: 6rem !important;
  }
  .okk-pc-pr60 {
    padding-right: 6rem !important;
  }
  .okk-pc-pb60 {
    padding-bottom: 6rem !important;
  }
  .okk-pc-pl60 {
    padding-left: 6rem !important;
  }
  .okk-pc-mt65 {
    margin-top: 6.5rem !important;
  }
  .okk-pc-mr65 {
    margin-right: 6.5rem !important;
  }
  .okk-pc-mb65 {
    margin-bottom: 6.5rem !important;
  }
  .okk-pc-ml65 {
    margin-left: 6.5rem !important;
  }
  .okk-pc-pt65 {
    padding-top: 6.5rem !important;
  }
  .okk-pc-pr65 {
    padding-right: 6.5rem !important;
  }
  .okk-pc-pb65 {
    padding-bottom: 6.5rem !important;
  }
  .okk-pc-pl65 {
    padding-left: 6.5rem !important;
  }
  .okk-pc-mt70 {
    margin-top: 7rem !important;
  }
  .okk-pc-mr70 {
    margin-right: 7rem !important;
  }
  .okk-pc-mb70 {
    margin-bottom: 7rem !important;
  }
  .okk-pc-ml70 {
    margin-left: 7rem !important;
  }
  .okk-pc-pt70 {
    padding-top: 7rem !important;
  }
  .okk-pc-pr70 {
    padding-right: 7rem !important;
  }
  .okk-pc-pb70 {
    padding-bottom: 7rem !important;
  }
  .okk-pc-pl70 {
    padding-left: 7rem !important;
  }
  .okk-pc-mt75 {
    margin-top: 7.5rem !important;
  }
  .okk-pc-mr75 {
    margin-right: 7.5rem !important;
  }
  .okk-pc-mb75 {
    margin-bottom: 7.5rem !important;
  }
  .okk-pc-ml75 {
    margin-left: 7.5rem !important;
  }
  .okk-pc-pt75 {
    padding-top: 7.5rem !important;
  }
  .okk-pc-pr75 {
    padding-right: 7.5rem !important;
  }
  .okk-pc-pb75 {
    padding-bottom: 7.5rem !important;
  }
  .okk-pc-pl75 {
    padding-left: 7.5rem !important;
  }
  .okk-pc-mt80 {
    margin-top: 8rem !important;
  }
  .okk-pc-mr80 {
    margin-right: 8rem !important;
  }
  .okk-pc-mb80 {
    margin-bottom: 8rem !important;
  }
  .okk-pc-ml80 {
    margin-left: 8rem !important;
  }
  .okk-pc-pt80 {
    padding-top: 8rem !important;
  }
  .okk-pc-pr80 {
    padding-right: 8rem !important;
  }
  .okk-pc-pb80 {
    padding-bottom: 8rem !important;
  }
  .okk-pc-pl80 {
    padding-left: 8rem !important;
  }
  .okk-pc-mt85 {
    margin-top: 8.5rem !important;
  }
  .okk-pc-mr85 {
    margin-right: 8.5rem !important;
  }
  .okk-pc-mb85 {
    margin-bottom: 8.5rem !important;
  }
  .okk-pc-ml85 {
    margin-left: 8.5rem !important;
  }
  .okk-pc-pt85 {
    padding-top: 8.5rem !important;
  }
  .okk-pc-pr85 {
    padding-right: 8.5rem !important;
  }
  .okk-pc-pb85 {
    padding-bottom: 8.5rem !important;
  }
  .okk-pc-pl85 {
    padding-left: 8.5rem !important;
  }
  .okk-pc-mt90 {
    margin-top: 9rem !important;
  }
  .okk-pc-mr90 {
    margin-right: 9rem !important;
  }
  .okk-pc-mb90 {
    margin-bottom: 9rem !important;
  }
  .okk-pc-ml90 {
    margin-left: 9rem !important;
  }
  .okk-pc-pt90 {
    padding-top: 9rem !important;
  }
  .okk-pc-pr90 {
    padding-right: 9rem !important;
  }
  .okk-pc-pb90 {
    padding-bottom: 9rem !important;
  }
  .okk-pc-pl90 {
    padding-left: 9rem !important;
  }
  .okk-pc-mt95 {
    margin-top: 9.5rem !important;
  }
  .okk-pc-mr95 {
    margin-right: 9.5rem !important;
  }
  .okk-pc-mb95 {
    margin-bottom: 9.5rem !important;
  }
  .okk-pc-ml95 {
    margin-left: 9.5rem !important;
  }
  .okk-pc-pt95 {
    padding-top: 9.5rem !important;
  }
  .okk-pc-pr95 {
    padding-right: 9.5rem !important;
  }
  .okk-pc-pb95 {
    padding-bottom: 9.5rem !important;
  }
  .okk-pc-pl95 {
    padding-left: 9.5rem !important;
  }
  .okk-pc-mt100 {
    margin-top: 10rem !important;
  }
  .okk-pc-mr100 {
    margin-right: 10rem !important;
  }
  .okk-pc-mb100 {
    margin-bottom: 10rem !important;
  }
  .okk-pc-ml100 {
    margin-left: 10rem !important;
  }
  .okk-pc-pt100 {
    padding-top: 10rem !important;
  }
  .okk-pc-pr100 {
    padding-right: 10rem !important;
  }
  .okk-pc-pb100 {
    padding-bottom: 10rem !important;
  }
  .okk-pc-pl100 {
    padding-left: 10rem !important;
  }
}
@media screen and (max-width: 767px) {
  .okk-sp-mt0 {
    margin-top: 0rem !important;
  }
  .okk-sp-mr0 {
    margin-right: 0rem !important;
  }
  .okk-sp-mb0 {
    margin-bottom: 0rem !important;
  }
  .okk-sp-ml0 {
    margin-left: 0rem !important;
  }
  .okk-sp-pt0 {
    padding-top: 0rem !important;
  }
  .okk-sp-pr0 {
    padding-right: 0rem !important;
  }
  .okk-sp-pb0 {
    padding-bottom: 0rem !important;
  }
  .okk-sp-pl0 {
    padding-left: 0rem !important;
  }
  .okk-sp-mt5 {
    margin-top: 0.5rem !important;
  }
  .okk-sp-mr5 {
    margin-right: 0.5rem !important;
  }
  .okk-sp-mb5 {
    margin-bottom: 0.5rem !important;
  }
  .okk-sp-ml5 {
    margin-left: 0.5rem !important;
  }
  .okk-sp-pt5 {
    padding-top: 0.5rem !important;
  }
  .okk-sp-pr5 {
    padding-right: 0.5rem !important;
  }
  .okk-sp-pb5 {
    padding-bottom: 0.5rem !important;
  }
  .okk-sp-pl5 {
    padding-left: 0.5rem !important;
  }
  .okk-sp-mt10 {
    margin-top: 1rem !important;
  }
  .okk-sp-mr10 {
    margin-right: 1rem !important;
  }
  .okk-sp-mb10 {
    margin-bottom: 1rem !important;
  }
  .okk-sp-ml10 {
    margin-left: 1rem !important;
  }
  .okk-sp-pt10 {
    padding-top: 1rem !important;
  }
  .okk-sp-pr10 {
    padding-right: 1rem !important;
  }
  .okk-sp-pb10 {
    padding-bottom: 1rem !important;
  }
  .okk-sp-pl10 {
    padding-left: 1rem !important;
  }
  .okk-sp-mt15 {
    margin-top: 1.5rem !important;
  }
  .okk-sp-mr15 {
    margin-right: 1.5rem !important;
  }
  .okk-sp-mb15 {
    margin-bottom: 1.5rem !important;
  }
  .okk-sp-ml15 {
    margin-left: 1.5rem !important;
  }
  .okk-sp-pt15 {
    padding-top: 1.5rem !important;
  }
  .okk-sp-pr15 {
    padding-right: 1.5rem !important;
  }
  .okk-sp-pb15 {
    padding-bottom: 1.5rem !important;
  }
  .okk-sp-pl15 {
    padding-left: 1.5rem !important;
  }
  .okk-sp-mt20 {
    margin-top: 2rem !important;
  }
  .okk-sp-mr20 {
    margin-right: 2rem !important;
  }
  .okk-sp-mb20 {
    margin-bottom: 2rem !important;
  }
  .okk-sp-ml20 {
    margin-left: 2rem !important;
  }
  .okk-sp-pt20 {
    padding-top: 2rem !important;
  }
  .okk-sp-pr20 {
    padding-right: 2rem !important;
  }
  .okk-sp-pb20 {
    padding-bottom: 2rem !important;
  }
  .okk-sp-pl20 {
    padding-left: 2rem !important;
  }
  .okk-sp-mt25 {
    margin-top: 2.5rem !important;
  }
  .okk-sp-mr25 {
    margin-right: 2.5rem !important;
  }
  .okk-sp-mb25 {
    margin-bottom: 2.5rem !important;
  }
  .okk-sp-ml25 {
    margin-left: 2.5rem !important;
  }
  .okk-sp-pt25 {
    padding-top: 2.5rem !important;
  }
  .okk-sp-pr25 {
    padding-right: 2.5rem !important;
  }
  .okk-sp-pb25 {
    padding-bottom: 2.5rem !important;
  }
  .okk-sp-pl25 {
    padding-left: 2.5rem !important;
  }
  .okk-sp-mt30 {
    margin-top: 3rem !important;
  }
  .okk-sp-mr30 {
    margin-right: 3rem !important;
  }
  .okk-sp-mb30 {
    margin-bottom: 3rem !important;
  }
  .okk-sp-ml30 {
    margin-left: 3rem !important;
  }
  .okk-sp-pt30 {
    padding-top: 3rem !important;
  }
  .okk-sp-pr30 {
    padding-right: 3rem !important;
  }
  .okk-sp-pb30 {
    padding-bottom: 3rem !important;
  }
  .okk-sp-pl30 {
    padding-left: 3rem !important;
  }
  .okk-sp-mt35 {
    margin-top: 3.5rem !important;
  }
  .okk-sp-mr35 {
    margin-right: 3.5rem !important;
  }
  .okk-sp-mb35 {
    margin-bottom: 3.5rem !important;
  }
  .okk-sp-ml35 {
    margin-left: 3.5rem !important;
  }
  .okk-sp-pt35 {
    padding-top: 3.5rem !important;
  }
  .okk-sp-pr35 {
    padding-right: 3.5rem !important;
  }
  .okk-sp-pb35 {
    padding-bottom: 3.5rem !important;
  }
  .okk-sp-pl35 {
    padding-left: 3.5rem !important;
  }
  .okk-sp-mt40 {
    margin-top: 4rem !important;
  }
  .okk-sp-mr40 {
    margin-right: 4rem !important;
  }
  .okk-sp-mb40 {
    margin-bottom: 4rem !important;
  }
  .okk-sp-ml40 {
    margin-left: 4rem !important;
  }
  .okk-sp-pt40 {
    padding-top: 4rem !important;
  }
  .okk-sp-pr40 {
    padding-right: 4rem !important;
  }
  .okk-sp-pb40 {
    padding-bottom: 4rem !important;
  }
  .okk-sp-pl40 {
    padding-left: 4rem !important;
  }
  .okk-sp-mt45 {
    margin-top: 4.5rem !important;
  }
  .okk-sp-mr45 {
    margin-right: 4.5rem !important;
  }
  .okk-sp-mb45 {
    margin-bottom: 4.5rem !important;
  }
  .okk-sp-ml45 {
    margin-left: 4.5rem !important;
  }
  .okk-sp-pt45 {
    padding-top: 4.5rem !important;
  }
  .okk-sp-pr45 {
    padding-right: 4.5rem !important;
  }
  .okk-sp-pb45 {
    padding-bottom: 4.5rem !important;
  }
  .okk-sp-pl45 {
    padding-left: 4.5rem !important;
  }
  .okk-sp-mt50 {
    margin-top: 5rem !important;
  }
  .okk-sp-mr50 {
    margin-right: 5rem !important;
  }
  .okk-sp-mb50 {
    margin-bottom: 5rem !important;
  }
  .okk-sp-ml50 {
    margin-left: 5rem !important;
  }
  .okk-sp-pt50 {
    padding-top: 5rem !important;
  }
  .okk-sp-pr50 {
    padding-right: 5rem !important;
  }
  .okk-sp-pb50 {
    padding-bottom: 5rem !important;
  }
  .okk-sp-pl50 {
    padding-left: 5rem !important;
  }
  .okk-sp-mt55 {
    margin-top: 5.5rem !important;
  }
  .okk-sp-mr55 {
    margin-right: 5.5rem !important;
  }
  .okk-sp-mb55 {
    margin-bottom: 5.5rem !important;
  }
  .okk-sp-ml55 {
    margin-left: 5.5rem !important;
  }
  .okk-sp-pt55 {
    padding-top: 5.5rem !important;
  }
  .okk-sp-pr55 {
    padding-right: 5.5rem !important;
  }
  .okk-sp-pb55 {
    padding-bottom: 5.5rem !important;
  }
  .okk-sp-pl55 {
    padding-left: 5.5rem !important;
  }
  .okk-sp-mt60 {
    margin-top: 6rem !important;
  }
  .okk-sp-mr60 {
    margin-right: 6rem !important;
  }
  .okk-sp-mb60 {
    margin-bottom: 6rem !important;
  }
  .okk-sp-ml60 {
    margin-left: 6rem !important;
  }
  .okk-sp-pt60 {
    padding-top: 6rem !important;
  }
  .okk-sp-pr60 {
    padding-right: 6rem !important;
  }
  .okk-sp-pb60 {
    padding-bottom: 6rem !important;
  }
  .okk-sp-pl60 {
    padding-left: 6rem !important;
  }
  .okk-sp-mt65 {
    margin-top: 6.5rem !important;
  }
  .okk-sp-mr65 {
    margin-right: 6.5rem !important;
  }
  .okk-sp-mb65 {
    margin-bottom: 6.5rem !important;
  }
  .okk-sp-ml65 {
    margin-left: 6.5rem !important;
  }
  .okk-sp-pt65 {
    padding-top: 6.5rem !important;
  }
  .okk-sp-pr65 {
    padding-right: 6.5rem !important;
  }
  .okk-sp-pb65 {
    padding-bottom: 6.5rem !important;
  }
  .okk-sp-pl65 {
    padding-left: 6.5rem !important;
  }
  .okk-sp-mt70 {
    margin-top: 7rem !important;
  }
  .okk-sp-mr70 {
    margin-right: 7rem !important;
  }
  .okk-sp-mb70 {
    margin-bottom: 7rem !important;
  }
  .okk-sp-ml70 {
    margin-left: 7rem !important;
  }
  .okk-sp-pt70 {
    padding-top: 7rem !important;
  }
  .okk-sp-pr70 {
    padding-right: 7rem !important;
  }
  .okk-sp-pb70 {
    padding-bottom: 7rem !important;
  }
  .okk-sp-pl70 {
    padding-left: 7rem !important;
  }
  .okk-sp-mt75 {
    margin-top: 7.5rem !important;
  }
  .okk-sp-mr75 {
    margin-right: 7.5rem !important;
  }
  .okk-sp-mb75 {
    margin-bottom: 7.5rem !important;
  }
  .okk-sp-ml75 {
    margin-left: 7.5rem !important;
  }
  .okk-sp-pt75 {
    padding-top: 7.5rem !important;
  }
  .okk-sp-pr75 {
    padding-right: 7.5rem !important;
  }
  .okk-sp-pb75 {
    padding-bottom: 7.5rem !important;
  }
  .okk-sp-pl75 {
    padding-left: 7.5rem !important;
  }
  .okk-sp-mt80 {
    margin-top: 8rem !important;
  }
  .okk-sp-mr80 {
    margin-right: 8rem !important;
  }
  .okk-sp-mb80 {
    margin-bottom: 8rem !important;
  }
  .okk-sp-ml80 {
    margin-left: 8rem !important;
  }
  .okk-sp-pt80 {
    padding-top: 8rem !important;
  }
  .okk-sp-pr80 {
    padding-right: 8rem !important;
  }
  .okk-sp-pb80 {
    padding-bottom: 8rem !important;
  }
  .okk-sp-pl80 {
    padding-left: 8rem !important;
  }
  .okk-sp-mt85 {
    margin-top: 8.5rem !important;
  }
  .okk-sp-mr85 {
    margin-right: 8.5rem !important;
  }
  .okk-sp-mb85 {
    margin-bottom: 8.5rem !important;
  }
  .okk-sp-ml85 {
    margin-left: 8.5rem !important;
  }
  .okk-sp-pt85 {
    padding-top: 8.5rem !important;
  }
  .okk-sp-pr85 {
    padding-right: 8.5rem !important;
  }
  .okk-sp-pb85 {
    padding-bottom: 8.5rem !important;
  }
  .okk-sp-pl85 {
    padding-left: 8.5rem !important;
  }
  .okk-sp-mt90 {
    margin-top: 9rem !important;
  }
  .okk-sp-mr90 {
    margin-right: 9rem !important;
  }
  .okk-sp-mb90 {
    margin-bottom: 9rem !important;
  }
  .okk-sp-ml90 {
    margin-left: 9rem !important;
  }
  .okk-sp-pt90 {
    padding-top: 9rem !important;
  }
  .okk-sp-pr90 {
    padding-right: 9rem !important;
  }
  .okk-sp-pb90 {
    padding-bottom: 9rem !important;
  }
  .okk-sp-pl90 {
    padding-left: 9rem !important;
  }
  .okk-sp-mt95 {
    margin-top: 9.5rem !important;
  }
  .okk-sp-mr95 {
    margin-right: 9.5rem !important;
  }
  .okk-sp-mb95 {
    margin-bottom: 9.5rem !important;
  }
  .okk-sp-ml95 {
    margin-left: 9.5rem !important;
  }
  .okk-sp-pt95 {
    padding-top: 9.5rem !important;
  }
  .okk-sp-pr95 {
    padding-right: 9.5rem !important;
  }
  .okk-sp-pb95 {
    padding-bottom: 9.5rem !important;
  }
  .okk-sp-pl95 {
    padding-left: 9.5rem !important;
  }
  .okk-sp-mt100 {
    margin-top: 10rem !important;
  }
  .okk-sp-mr100 {
    margin-right: 10rem !important;
  }
  .okk-sp-mb100 {
    margin-bottom: 10rem !important;
  }
  .okk-sp-ml100 {
    margin-left: 10rem !important;
  }
  .okk-sp-pt100 {
    padding-top: 10rem !important;
  }
  .okk-sp-pr100 {
    padding-right: 10rem !important;
  }
  .okk-sp-pb100 {
    padding-bottom: 10rem !important;
  }
  .okk-sp-pl100 {
    padding-left: 10rem !important;
  }
}
@-webkit-keyframes fadezoom {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  100% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
}
@keyframes fadezoom {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  100% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
}
@-webkit-keyframes animate-svg-stroke {
  0% {
    stroke-dashoffset: 159.0796326795px;
    stroke-dasharray: 159.0796326795px;
  }
  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 159.0796326795px;
  }
}
@keyframes animate-svg-stroke {
  0% {
    stroke-dashoffset: 159.0796326795px;
    stroke-dasharray: 159.0796326795px;
  }
  100% {
    stroke-dashoffset: 0;
    stroke-dasharray: 159.0796326795px;
  }
}
@-webkit-keyframes scrollAboutDown {
  0% {
    background-position-y: 0;
  }
  to {
    background-position-y: 125.3rem;
  }
}
@keyframes scrollAboutDown {
  0% {
    background-position-y: 0;
  }
  to {
    background-position-y: 125.3rem;
  }
}
@-webkit-keyframes scrollAboutUp {
  0% {
    background-position-y: 0;
  }
  to {
    background-position-y: -125.3rem;
  }
}
@keyframes scrollAboutUp {
  0% {
    background-position-y: 0;
  }
  to {
    background-position-y: -125.3rem;
  }
}
@-webkit-keyframes scrollAboutRight {
  0% {
    background-position-x: 0;
  }
  to {
    background-position-x: 68.7rem;
  }
}
@keyframes scrollAboutRight {
  0% {
    background-position-x: 0;
  }
  to {
    background-position-x: 68.7rem;
  }
}
@-webkit-keyframes scrollAboutLeft {
  0% {
    background-position-x: 0;
  }
  to {
    background-position-x: -68.7rem;
  }
}
@keyframes scrollAboutLeft {
  0% {
    background-position-x: 0;
  }
  to {
    background-position-x: -68.7rem;
  }
}
a {
  color: inherit;
  overflow: unset;
}

sup {
  vertical-align: super;
  font-size: smaller;
}

.okk-body {
  background: #fff;
  color: #413e3e;
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 400;
  letter-spacing: 0;
}
.okk-body #wrapper {
  padding-top: 14.1rem;
}
@media screen and (max-width: 767px) {
  .okk-body #wrapper {
    padding-top: 11.2rem !important;
  }
}
@media screen and (min-width: 768px) {
  .okk-body a[href^=tel] {
    pointer-events: none;
    text-decoration: none;
    opacity: 1 !important;
    cursor: default !important;
  }
}
@media screen and (max-width: 767px) {
  .okk-body {
    font-size: 1.4rem;
  }
}

.body-no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.okk-home {
  font-family: "Noto Sans JP", "ヒラギノ角ゴ ProN", "ヒラギノ角ゴ Pro", "ヒラギノ角ゴ StdN", "Hiragino Kaku Gothic ProN", "YuGothic", "游ゴシック", "Yu Gothic", "游ゴシック Medium", "Yu Gothic Medium", "ヒラギノ⾓ゴ Pro", "Hiragino Kaku Gothic Pro", "MS PGothic", sans-serif;
}
@media screen and (min-width: 768px) {
  .okk-home #wrapper {
    padding-top: 0 !important;
  }
}

.okk-main {
  background: #fff !important;
}

.okk-img-fit, .okk-reserve-store__img, .okk-about-greeting__img, .okk-about-feature__img, .okk-about-history .figure, .okk-about-point__img, .okk-t-corporate__img, .okk-t-cover-woman__bnr, .okk-t-publication__link .publication-img, .okk-t-formal__btn .formal-bg, .okk-t-column__link .column-img, .okk-store-other__link .img, .okk-ls-storeinfo__link .storeinfo-img, .okk-slider-gallery__photo figure, .okk-t-hairset__photo li, .okk-t-rentalplan__link .rentalplan-img figure, .okk-t-movie__btn .moive-bg, .okk-t-point .point-img, .okk-t-links__link .links-img, .okk-t-kv .slide-item, .okk-header-corporate__img {
  overflow: hidden;
}
.okk-img-fit img, .okk-reserve-store__img img, .okk-about-greeting__img img, .okk-about-feature__img img, .okk-about-history .figure img, .okk-about-point__img img, .okk-t-corporate__img img, .okk-t-cover-woman__bnr img, .okk-t-publication__link .publication-img img, .okk-t-formal__btn .formal-bg img, .okk-t-column__link .column-img img, .okk-store-other__link .img img, .okk-ls-storeinfo__link .storeinfo-img img, .okk-slider-gallery__photo figure img, .okk-t-hairset__photo li img, .okk-t-rentalplan__link .rentalplan-img figure img, .okk-t-movie__btn .moive-bg img, .okk-t-point .point-img img, .okk-t-links__link .links-img img, .okk-t-kv .slide-item img, .okk-header-corporate__img img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.okk-red {
  color: #d98794;
}

.inline-block {
  display: inline-block;
}

.overflow-hidden {
  overflow: hidden;
}

.okk-bg-gray {
  background: #f2f5f7;
}

.okk-bg-rose {
  background: #faf2f4;
}

.hover, .okk-reserve-store__btn, .okk-anchor02__btn, .okk-anchor__btn, .okk-store-google-map__iframe a, .okk-store-anchor a, .okk-store__slider .slick-arrow, .okk-map-modal__slider .slick-arrow, .okk-t-corporate__link, .okk-t-cover-woman__bnr, .okk-t-publication__link, .okk-t-formal__btn, .okk-t-column__link, .okk-ls-storeinfo__link, .okk-t-rentalplan__link, .okk-video-popup .close-btn, .okk-t-movie__btn, .okk-t-point__link, .slider-scrollbar-list .slick-arrow, .okk-footer-copyright a, .okk-footer-fc a, .okk-footer-language a, .okk-footer-nav a, .okk-footer-logo a, .okk-footer-sns__list a, .okk-pagetop.is-active, .okk-header-sns__list li a, .okk-header-corporate__link, .okk-header-nav__list > li a, .okk-header-nav-pc__list > li a, .okk-header-hamburger a, .okk-header-reserve__cancel, .okk-header-reserve__btn a, .okk-header .okk-box-lang__list a, .okk-header__logo a, .okk-btn {
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
@media screen and (min-width: 768px) {
  .hover:hover, .okk-reserve-store__btn:hover, .okk-anchor02__btn:hover, .okk-anchor__btn:hover, .okk-store-google-map__iframe a:hover, .okk-store-anchor a:hover, .okk-store__slider .slick-arrow:hover, .okk-map-modal__slider .slick-arrow:hover, .okk-t-corporate__link:hover, .okk-t-cover-woman__bnr:hover, .okk-t-publication__link:hover, .okk-t-formal__btn:hover, .okk-t-column__link:hover, .okk-ls-storeinfo__link:hover, .okk-t-rentalplan__link:hover, .okk-video-popup .close-btn:hover, .okk-t-movie__btn:hover, .okk-t-point__link:hover, .slider-scrollbar-list .slick-arrow:hover, .okk-footer-copyright a:hover, .okk-footer-fc a:hover, .okk-footer-language a:hover, .okk-footer-nav a:hover, .okk-footer-logo a:hover, .okk-footer-sns__list a:hover, .okk-pagetop.is-active:hover, .okk-header-sns__list li a:hover, .okk-header-corporate__link:hover, .okk-header-nav__list > li a:hover, .okk-header-nav-pc__list > li a:hover, .okk-header-hamburger a:hover, .okk-header-reserve__cancel:hover, .okk-header-reserve__btn a:hover, .okk-header .okk-box-lang__list a:hover, .okk-header__logo a:hover, .okk-btn:hover {
    opacity: 0.75;
  }
}

.okk-inner {
  max-width: 119.6rem;
  margin: 0 auto;
  padding: 0 2rem;
}

.okk-btn-wrap {
  margin-top: 4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2rem;
}
@media screen and (max-width: 767px) {
  .okk-btn-wrap {
    margin-top: 3rem;
    gap: 1rem;
  }
}

.okk-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 2px solid #413e3e;
  font-size: 1.6rem;
  line-height: 1.5;
  padding: 2.1rem 4.5rem;
  text-align: center;
  position: relative;
  font-weight: 700;
  border-radius: 7rem;
  width: 40rem;
  max-width: 100%;
  background: #fff;
}
.okk-btn::after {
  position: absolute;
  top: 50%;
  right: 2.8rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  content: "";
  z-index: 1;
  width: 1.5rem;
  height: 1.1727272727rem;
  background: url("../img/common/ico_arrow_right.png") no-repeat right center/100% 100%;
}
.okk-btn--primary {
  background: #d98794;
  border-color: #d98794;
  color: #fff;
}
.okk-btn--primary::after {
  background-image: url("../img/common/ico_arrow_right_white.png");
}
.okk-btn--tel::after {
  content: none;
}
.okk-btn--sm {
  padding-top: 1.6rem;
  padding-bottom: 1.6rem;
}
@media screen and (max-width: 767px) {
  .okk-btn {
    padding: 1.6rem 4rem;
    border-radius: 6rem;
    width: 33.5rem;
    max-width: 100%;
  }
  .okk-btn::after {
    right: 2.6rem;
    width: 1.3rem;
    height: 1.01636364rem;
  }
  .okk-btn--sm {
    padding-top: 1.3rem;
    padding-bottom: 1.3rem;
    font-size: 1.4rem;
    line-height: 1.4285714286;
  }
}

.mfp-zoom {
  cursor: default;
}
.mfp-zoom.mfp-bg {
  opacity: 0;
  background: #000;
  cursor: default;
  -webkit-transition: 0.25s ease-out;
  transition: 0.25s ease-out;
}
.mfp-zoom.mfp-bg.mfp-ready {
  opacity: 0.8;
}
.mfp-zoom.mfp-bg.mfp-removing {
  opacity: 0;
}
.mfp-zoom.mfp-wrap .mfp-content {
  opacity: 0;
  -webkit-transform: scale(0.85);
          transform: scale(0.85);
  -webkit-transition: 0.25s ease-out;
  transition: 0.25s ease-out;
}
.mfp-zoom.mfp-wrap.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
}
.mfp-zoom.mfp-wrap.mfp-removing .mfp-content {
  opacity: 0;
  -webkit-transform: scale(0.85);
          transform: scale(0.85);
}

.okk-header {
  --currentColor: #413e3e;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  color: var(--currentColor);
}
.okk-header-top {
  position: relative;
  z-index: 1000;
}
.okk-header-top::before {
  position: absolute;
  content: none;
  background: #fff;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  -webkit-transform: translateY(-101%);
          transform: translateY(-101%);
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-home .okk-header-top::before {
  content: "";
}
.okk-header-top__wrap {
  position: relative;
  z-index: 2;
  background: transparent;
}
.okk-header-top__wrap::after {
  position: absolute;
  content: "";
  z-index: 1;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #c6c6c6;
}
.okk-header-top__inner {
  position: relative;
  z-index: 3;
  max-width: 129.2rem;
  padding: 0 3rem;
  margin: 0 auto;
}
.okk-header-top__row {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.okk-header__site {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  max-width: 79.3rem;
  padding-right: 2rem;
  padding-bottom: 1rem;
}
.okk-header__logo {
  width: 11.9rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-header__logo a {
  display: block;
}
.okk-header__slogan {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0.5rem 0 0.5rem 2rem;
  font-size: 1.2rem;
  line-height: 1.6666666667;
}
.okk-header__slogan span {
  display: inline-block;
}
.okk-header__group-btn {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-top: 1.8rem;
  padding-bottom: 1.2rem;
  padding-left: 18.5rem;
  z-index: 10;
  gap: 2rem;
}
.okk-header .okk-box-lang {
  position: absolute;
  top: 1.8rem;
  left: 0;
  width: 17.2rem;
  border: 2px solid #d98794;
  background: #fff;
  cursor: pointer;
  border-radius: 2.3rem;
  color: #d98794;
}
.okk-header .okk-box-lang__label {
  display: block;
  font-size: 1.6rem;
  line-height: 1.5;
  padding: 0.8rem 2rem 1rem 5.5rem;
  position: relative;
  font-weight: 500;
}
.okk-header .okk-box-lang__label::before {
  position: absolute;
  content: "";
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  top: 50%;
  left: 2.1rem;
  width: 2.5rem;
  height: 2.5rem;
  background: url("../img/common/ico_language.png") no-repeat center center/100% 100%;
}
.okk-header .okk-box-lang__list {
  width: 100%;
  display: none;
  padding: 0.5rem 1rem;
}
.okk-header .okk-box-lang__list li {
  border-top: 1px dotted #c3c3c3;
  padding: 0.5rem 1rem 0.5rem 4.5rem;
}
.okk-header .okk-box-lang__list.is-active {
  opacity: 1;
}
.okk-header-reserve__btn a {
  color: #fff;
  background: #d98794;
  padding: 1rem 2rem 1.2rem;
  width: 18.7rem;
  border-radius: 5rem;
  height: 5rem;
  text-align: center;
  font-weight: 700;
  line-height: 1.5;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-header-reserve__cancel {
  margin-top: 0.3rem;
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 500;
  text-decoration: underline;
  text-align: center;
}
.okk-header-hamburger {
  margin-top: 1.3rem;
  width: 4rem;
}
.okk-header-hamburger .hamburger-line {
  margin: 0 auto;
  width: 34px;
  height: 12px;
  position: relative;
  display: block;
}
.okk-header-hamburger .hamburger-line::before, .okk-header-hamburger .hamburger-line::after {
  position: absolute;
  content: "";
  background: var(--currentColor);
  width: 100%;
  height: 3px;
  z-index: 1;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-header-hamburger .hamburger-line::before {
  top: 0;
}
.okk-header-hamburger .hamburger-line::after {
  bottom: 0;
}
.okk-header-hamburger .hamburger-txt {
  position: relative;
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 500;
  font-weight: 600;
  display: block;
  text-align: center;
  width: 100%;
  height: 17px;
  width: calc(100% + 2rem);
  position: relative;
  margin: 0.6rem -1rem 0;
}
.okk-header-hamburger .hamburger-txt > span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
}
.okk-header-hamburger .hamburger-txt .txt4normal {
  opacity: 1;
}
.okk-header-hamburger .hamburger-txt .txt4active {
  opacity: 0;
}
.okk-header-nav-pc {
  position: relative;
  z-index: 1;
  opacity: 1;
  visibility: visible;
}
.okk-header-nav-pc__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.okk-header-nav-pc__list > li {
  padding: 0.8rem 2rem;
}
.okk-header-nav-pc__list > li a {
  line-height: 1.5;
}
.okk-header-nav-pc__list > li.menu-item-hasSub {
  position: relative;
}
.okk-header-nav-pc__list > li.menu-item-hasSub > a {
  position: relative;
  padding-right: 2rem;
}
.okk-header-nav-pc__list > li.menu-item-hasSub > a::after {
  position: absolute;
  content: "";
  top: 54%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 0;
  z-index: 1;
  width: 1.2rem;
  height: 0.7rem;
  background: url("../img/common/ico_arrow_menu_down_gray.png") no-repeat center center/100% 100%;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}
.okk-home .okk-header-nav-pc__list > li.menu-item-hasSub > a::after {
  background-image: url("../img/common/ico_arrow_menu_down.png");
}
.okk-header-nav-pc__list > li.menu-item-hasSub > ul {
  position: absolute;
  top: 100%;
  left: 50%;
  width: 14rem;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: #fff;
  color: #413e3e;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  text-align: left;
  z-index: 1;
}
.okk-header-nav-pc__list > li.menu-item-hasSub > ul li {
  padding-left: 1.5rem;
}
.okk-header-nav-pc__list > li.menu-item-hasSub > ul li:nth-child(n+2) {
  border-top: 1px solid #c5c5c5;
}
.okk-header-nav-pc__list > li.menu-item-hasSub > ul li a {
  padding: 1rem 0;
  display: block;
  font-size: 1.4rem;
  line-height: 1.4285714286;
}
.okk-header-nav-pc__list > li.menu-item-hasSub:hover > a::after {
  -webkit-transform: translateY(-50%) rotate(180deg);
          transform: translateY(-50%) rotate(180deg);
}
.okk-header-nav-pc__list > li.menu-item-hasSub:hover > ul {
  max-height: 75rem;
  opacity: 1;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.okk-header-main {
  position: fixed;
  width: 100%;
  max-height: 100dvh;
  max-height: calc(var(--vh, 1vh) * 100);
  left: 0;
  top: 0;
  padding-top: 10rem;
  z-index: 999;
  color: #413e3e;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.25s;
  transition: 0.25s;
  overflow-y: auto;
}
.okk-header-main__inner {
  padding: 4rem 3rem 6rem;
  background: #fff;
  border-radius: 0 0 2.5rem 2.5rem;
}
.okk-header-main__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  max-width: 115.5rem;
  margin: 0 auto;
}
.okk-header-nav {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  max-width: calc(100% - 29.2rem);
}
.okk-header-nav__col {
  width: 33.333%;
  max-width: 28.7rem;
}
@media screen and (min-width: 768px) {
  .okk-header-nav dl {
    width: 23rem;
    max-width: 94%;
  }
}
.okk-header-nav dl:nth-child(n+2) {
  margin-top: 3rem;
}
.okk-header-nav dl dt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 1.2rem;
  border-bottom: 1px solid #c6c6c6;
}
.okk-header-nav dl dt img {
  width: 2.5rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-header-nav dl dt .txt {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding-left: 0.5rem;
  font-size: 2rem;
  line-height: 1.45;
  font-weight: 700;
  color: #d98794;
}
@media screen and (min-width: 768px) {
  .okk-header-nav dl dt {
    pointer-events: none;
  }
  .okk-header-nav dl dd {
    display: block !important;
  }
}
.okk-header-nav__list > li {
  padding-top: 1.2rem;
}
.okk-header-nav__list > li a {
  line-height: 1.5;
  font-weight: 500;
}
.okk-header-nav__list > li > ul {
  padding-top: 0.4rem;
}
.okk-header-nav__list > li > ul li {
  margin-top: 0.8rem;
  line-height: 1;
}
.okk-header-nav__list > li > ul li a {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  position: relative;
  padding-left: 1.3rem;
}
.okk-header-nav__list > li > ul li a::before {
  position: absolute;
  content: "";
  left: 0;
  top: 1rem;
  background: #d98794;
  width: 0.9rem;
  height: 1px;
}
.okk-header-corporate {
  padding-top: 2.3rem;
  width: 29.2rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-header-corporate__list li {
  margin-bottom: 1.4rem;
}
.okk-header-corporate__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.9rem;
}
.okk-header-corporate__img {
  aspect-ratio: 1/1;
  border-radius: 0.6rem;
  width: 7.5rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-header-corporate__desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.okk-header-corporate__copy {
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 400;
}
.okk-header-corporate__ttl {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 700;
  margin-top: 0.4rem;
}
.okk-header-corporate__link {
  padding: 0.7rem;
  display: block;
  background: #fff;
  border-radius: 1.2rem;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  position: relative;
}
.okk-header-corporate__link::after {
  position: absolute;
  top: 50%;
  right: 1.4rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
  content: "";
  background: no-repeat center center/100% 100%;
  width: 1.5rem;
  height: 1.5rem;
}
.okk-header-corporate__link[target=_blank]::after {
  background-image: url("../img/common/ico_extend.png");
}
.okk-header-sns {
  background: #f8f8f8;
  padding: 1rem;
  border-radius: 1.2rem;
}
.okk-header-sns__headline {
  text-align: center;
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 700;
  color: #898989;
  margin-bottom: 0.7rem;
}
.okk-header-sns__list {
  padding-bottom: 0.2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 1.8rem;
}
.okk-header-sns__list li {
  width: 3.5rem;
}
.okk-header-sns__list li a {
  display: block;
}
.okk-header.is-fixed .okk-header-top::before {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.okk-header.is-fixed .okk-header-top__wrap {
  border-bottom-color: #c6c6c6;
}
.okk-home .okk-header.is-fixed .okk-header-nav-pc__list > li.menu-item-hasSub > a::after {
  background-image: url("../img/common/ico_arrow_menu_down_gray.png");
}
.okk-header.is-open .okk-header-top__wrap {
  background: #fff;
  border-bottom-color: #c6c6c6;
}
.okk-header.is-open .okk-header-nav-pc {
  opacity: 0;
  visibility: hidden;
}
.okk-header.is-open .okk-header-hamburger .hamburger-line::before {
  -webkit-transform: translateY(4px) rotateZ(28deg);
          transform: translateY(4px) rotateZ(28deg);
}
.okk-header.is-open .okk-header-hamburger .hamburger-line::after {
  -webkit-transform: translateY(-4px) rotateZ(-28deg);
          transform: translateY(-4px) rotateZ(-28deg);
}
.okk-header.is-open .okk-header-hamburger .hamburger-txt .txt4normal {
  opacity: 0;
}
.okk-header.is-open .okk-header-hamburger .hamburger-txt .txt4active {
  opacity: 1;
}
.okk-header.is-open .okk-header-main {
  opacity: 1;
  visibility: visible;
}
@media screen and (min-width: 768px) {
  .okk-home .okk-header {
    --currentColor: #fff;
  }
  .okk-home .okk-header.is-fixed, .okk-home .okk-header.is-open {
    --currentColor: #413e3e;
  }
}
@media screen and (min-width: 768px) {
  .okk-body:not(.okk-home) .okk-header-top {
    background: #fff;
  }
}
@media screen and (max-width: 767px) {
  .okk-header-top {
    background: #fff;
  }
  .okk-header-top::before {
    content: none;
  }
  .okk-header-top__wrap {
    border-bottom: none;
  }
  .okk-header-top__inner {
    padding: 0 2rem;
  }
  .okk-header-top__row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .okk-header__site {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    max-width: 100%;
    padding-right: 4rem;
    padding-bottom: 0;
  }
  .okk-header__logo {
    padding: 1.1rem 0;
    width: 8.9rem;
  }
  .okk-header__slogan {
    padding: 0rem 0 0rem 1rem;
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-header__slogan {
    padding-right: 0.8rem;
    font-size: 1rem;
    line-height: 1.5;
  }
}
@media screen and (max-width: 767px) {
  .okk-header__group-btn {
    position: static;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
    padding: 0;
    gap: 0;
    margin: 0 -2rem;
    width: calc(100% + 4rem);
  }
  .okk-header .okk-box-lang {
    position: fixed;
    z-index: 1001;
    top: 1.3rem;
    width: 11rem;
    left: auto;
    right: 0.8rem;
    border: none;
    border-radius: 0;
    color: #d98794;
    text-align: center;
    background: transparent;
  }
  .okk-header .okk-box-lang__label {
    width: 4.3rem;
    margin-left: auto;
    display: block;
    font-size: 1.2rem;
    line-height: 1.4166666667;
    padding: 2.1rem 0 0;
  }
  .okk-header .okk-box-lang__label::before {
    left: 50%;
    top: 0;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    width: 2.1rem;
    height: 2.1rem;
  }
  .okk-header .okk-box-lang__list {
    border: 1px solid #d98794;
    -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
            box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
    margin-top: 0.7rem;
    padding: 1rem;
    border-radius: 1rem;
    overflow: hidden;
    background: #fff;
  }
  .okk-header .okk-box-lang__list li {
    padding: 0.7rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    border-top-width: 1px;
  }
  .okk-header .okk-box-lang__list li:first-child {
    border-top: none;
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-header .okk-box-lang {
    width: 7.2rem;
    top: 1.4rem;
  }
  .okk-header .okk-box-lang__label {
    font-size: 1rem;
    line-height: 1.5;
  }
  .okk-header .okk-box-lang__list {
    border-width: 0.5px;
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.6rem;
  }
  .okk-header .okk-box-lang__list li {
    padding: 0.7rem 0;
    font-size: 1rem;
    border-top-width: 0.5px;
  }
}
@media screen and (max-width: 767px) {
  .okk-header-reserve {
    width: 50%;
  }
  .okk-header-reserve__btn a {
    padding: 0.6rem 2.15rem 0.6rem 2rem;
    width: 100%;
    height: 5.2rem;
    border-radius: 0;
    line-height: 1.4285714286;
    position: relative;
  }
  .okk-header-reserve__btn a::after {
    position: absolute;
    content: "";
    z-index: 1;
    top: 54%;
    right: 1.3rem;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
    height: 1.1rem;
    width: 0.605rem;
    background: url("../img/common/ico_arrow_menu_right.png") no-repeat center center/100% 100%;
  }
  .okk-header-reserve__cancel {
    font-size: 1.4rem;
    line-height: 1.4285714286;
    background: #f8f8f8;
    padding: 1.3rem 1rem;
    text-align: center;
    border-radius: 1rem;
    width: 100%;
    max-width: 33.5rem;
    margin: 0 auto;
  }
  .okk-header-hamburger {
    border-top: 1px solid #c3c3c3;
    padding-top: 0.4rem;
    margin-top: 0;
    width: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .okk-header-hamburger a {
    width: 3.2rem;
  }
  .okk-header-hamburger .hamburger-line {
    width: 3.1rem;
    height: 1rem;
  }
  .okk-header-hamburger .hamburger-line::before, .okk-header-hamburger .hamburger-line::after {
    height: 2px;
  }
  .okk-header-hamburger .hamburger-txt {
    font-size: 1rem;
    line-height: 1.5;
    height: 15px;
    margin: 0.4rem -1rem 0;
  }
  .okk-header-nav-pc {
    border-top: none;
  }
  .okk-header-nav-pc__list {
    padding: 1rem 0;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .okk-header-nav-pc__list > li {
    width: 50%;
    font-size: 1.4rem;
  }
  .okk-header-main {
    padding-top: 11.2rem;
  }
  .okk-header-main__inner {
    padding: 1.8rem 2rem 3rem;
    border-radius: 0;
  }
  .okk-header-main__row {
    display: block;
    width: 100%;
    max-width: 33.5rem;
    margin: 0 auto;
  }
  .okk-header-nav {
    padding-right: 0;
    display: block;
    width: 100%;
    max-width: 100%;
  }
  .okk-header-nav__col {
    width: 100%;
    max-width: 100%;
  }
  .okk-header-nav dl:nth-child(n+2) {
    margin-top: 0;
  }
  .okk-header-nav dl dt {
    padding-top: 2rem;
    padding-bottom: 1.8rem;
    position: relative;
  }
  .okk-header-nav dl dt::before, .okk-header-nav dl dt::after {
    position: absolute;
    content: "";
    z-index: 1;
    margin-top: 0.2rem;
    width: 1.8rem;
    height: 1px;
    background: #413e3e;
    right: 1rem;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
  }
  .okk-header-nav dl dt::after {
    -webkit-transform: translateY(-50%) rotate(90deg);
            transform: translateY(-50%) rotate(90deg);
    -webkit-transition: 0.25s;
    transition: 0.25s;
  }
  .okk-header-nav dl dt img {
    width: 2.5rem;
  }
  .okk-header-nav dl dt .txt {
    font-size: 1.8rem;
    line-height: 1.4444444444;
  }
  .okk-header-nav dl dt.is-active::after {
    opacity: 0;
  }
  .okk-header-nav dl dd {
    padding-bottom: 2rem;
  }
  .okk-header-nav__list {
    padding-top: 0.4rem;
  }
  .okk-header-nav__list > li {
    padding-top: 1.4rem;
  }
  .okk-header-nav__list > li > a {
    font-size: 1.6rem;
  }
  .okk-header-nav__list > li > ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    padding-bottom: 1.2rem;
  }
  .okk-header-nav__list > li > ul li {
    margin-top: 1rem;
    width: 50%;
  }
  .okk-header-nav__list > li > ul li a {
    display: inline-block;
    padding-left: 2rem;
  }
  .okk-header-nav__list > li > ul li a::before {
    content: none;
  }
  .okk-header-corporate {
    padding-top: 4rem;
    width: 100%;
    max-width: 35.5rem;
  }
  .okk-header-corporate__list li {
    margin-bottom: 1.2rem;
  }
  .okk-header-corporate__inner {
    gap: 1rem;
  }
  .okk-header-corporate__img {
    width: 7.6rem;
  }
  .okk-header-corporate__ttl {
    font-size: 2rem;
    line-height: 1.45;
  }
  .okk-header-corporate__link {
    border-radius: 1rem;
  }
  .okk-header-corporate__link::after {
    right: 1.2rem;
    width: 1.6rem;
    height: 1.6rem;
  }
  .okk-header-sns {
    padding: 1.3rem;
    border-radius: 1rem;
  }
  .okk-header-sns__list {
    gap: 2rem;
  }
  .okk-header.is-fixed .okk-header-top::before {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  .okk-header.is-fixed .okk-header-nav-pc {
    border-top-color: #c6c6c6;
  }
  .okk-header.is-fixed .okk-header-nav-pc__list > li.menu-item-hasSub > a::after {
    background-image: url(../img/common/ico_arrow_menu_down_gray.png);
  }
  .okk-header.is-open .okk-header-top__wrap {
    background: #fff;
  }
  .okk-header.is-open .okk-header-nav-pc {
    opacity: 0;
    visibility: hidden;
  }
  .okk-header.is-open .okk-header-main {
    opacity: 1;
    visibility: visible;
  }
}

.okk-pagetop {
  position: fixed;
  right: 3rem;
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: opacity 0.25s !important;
  transition: opacity 0.25s !important;
  cursor: pointer;
  width: 6.7rem;
  height: 6.7rem;
  bottom: 3rem;
}
.okk-pagetop.is-active {
  visibility: visible;
  opacity: 1;
}
.okk-pagetop.is-absolute {
  position: absolute;
}
@media screen and (max-width: 767px) {
  .okk-pagetop {
    right: 2rem;
    width: 5.6rem;
    height: 5.6rem;
  }
}

.okk-footer {
  position: relative;
}
.okk-footer-headline {
  font-size: 2.6rem;
  line-height: 1.4230769231;
  font-weight: 700;
}
.okk-footer__top {
  text-align: center;
  background: #f8f8f8;
}
.okk-footer-sns {
  padding: 4rem 0;
}
.okk-footer-sns__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1.5rem;
}
.okk-footer-sns__list li {
  width: 4.6rem;
}
.okk-footer-cta {
  padding: 4rem 0;
  border-top: 1px solid #c3c3c3;
}
.okk-footer-cta__btn {
  max-width: 40rem;
  margin: 1.4rem auto 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.okk-footer-call {
  display: none;
  border-top: 1px solid #c3c3c3;
  padding: 4rem 0;
}
.okk-footer-call__copy {
  margin-top: 0.4rem;
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 400;
}
.okk-footer-call__list {
  margin-top: 2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2rem;
}
.okk-footer-call__list li {
  width: 13.8rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-footer-call__list a {
  display: block;
  padding: 1rem 0;
  background: #fff;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 1.5rem;
  font-size: 1.6rem;
  line-height: 1.5;
}
.okk-footer-call__list .store-name {
  color: #d98794;
  font-weight: 700;
}
.okk-footer-call__list .tel-number {
  display: block;
}
.okk-footer-logo {
  padding: 5rem 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-footer-logo a {
  width: 19.3rem;
}
.okk-footer-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-footer-nav li {
  padding: 0 1rem;
}
.okk-footer-nav li:nth-child(n+2) {
  position: relative;
}
.okk-footer-nav li:nth-child(n+2)::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 0;
  width: 1px;
  height: 1.7rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background: #413e3e;
}
.okk-footer-nav a {
  line-height: 1.5;
}
@media screen and (max-width: 767px) {
  .okk-footer-nav li:nth-child(1) {
    padding-left: 0;
  }
  .okk-footer-nav li:nth-child(3) {
    padding-right: 0;
  }
  .okk-footer-nav li a {
    font-size: 1.2rem;
  }
}
.okk-footer-language {
  padding-top: 0.4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-footer-language li {
  margin-top: 1rem;
  line-height: 1;
  padding: 0 1rem;
}
.okk-footer-language li:nth-child(n+2) {
  border-left: 1px solid #413e3e;
}
.okk-footer-language a {
  display: block;
  font-size: 1rem;
  line-height: 1.5;
}
.okk-footer-fc {
  padding: 3rem 0 4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 2.8rem;
}
.okk-footer-fc a {
  width: 25.4rem;
  max-width: 46%;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  display: block;
}
.okk-footer-copyright {
  border-top: 1px solid #c3c3c3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 3rem 0 4rem;
  font-size: 1rem;
  line-height: 1.5;
}
@media screen and (max-width: 767px) {
  .okk-footer-headline {
    font-size: 1.8rem;
    line-height: 1.5555555556;
  }
  .okk-footer-sns {
    padding: 3.2rem 0 2.8rem;
  }
  .okk-footer-sns__list {
    gap: 2.2rem;
  }
  .okk-footer-sns__list li {
    width: 3.8rem;
  }
  .okk-footer-cta {
    padding: 3rem 0 4rem;
  }
  .okk-footer-cta__btn {
    max-width: 33.5rem;
    margin: 2rem auto 0;
  }
  .okk-footer-call {
    padding: 3rem 0;
  }
  .okk-footer-call__copy {
    margin-top: 0.6rem;
  }
  .okk-footer-call__list {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    gap: 1.1rem;
  }
  .okk-footer-call__list li {
    width: 16.2rem;
    max-width: calc(50% - 0.55rem);
  }
  .okk-footer-call__list a {
    border-radius: 1rem;
  }
  .okk-footer-logo {
    padding: 3rem 0 2.6rem;
  }
  .okk-footer-logo a {
    width: 12.8rem;
  }
  .okk-footer-nav li:nth-child(n+2)::before {
    height: 1.6rem;
  }
  .okk-footer-language {
    display: block;
    max-width: 33.5rem;
    margin: 2.6rem auto 0;
  }
  .okk-footer-language li {
    padding: 0;
  }
  .okk-footer-language li:nth-child(n+2) {
    margin-top: 1rem;
    border-left: none;
  }
  .okk-footer-language a {
    font-size: 1.2rem;
    line-height: 1.4166666667;
    text-decoration: underline;
  }
  .okk-footer-fc {
    padding: 2.6rem 0 3rem;
    gap: 1.1rem;
  }
  .okk-footer-fc a {
    width: 16.2rem;
    max-width: calc(50% - 0.55rem);
  }
  .okk-footer-copyright {
    padding: 2rem 0;
  }
}

.okk-section-wrap {
  padding: 6rem 0;
}
@media screen and (max-width: 767px) {
  .okk-section-wrap {
    padding: 5rem 0 6rem;
  }
}

.okk-section-head {
  text-align: center;
  margin-bottom: 4rem;
}
@media screen and (max-width: 767px) {
  .okk-section-head {
    margin-bottom: 3rem;
  }
}

.okk-page-title {
  text-align: center;
  color: #d98794;
  font-size: 2.4rem;
  line-height: 1.5833333333;
  font-weight: 700;
  letter-spacing: 0;
  margin-bottom: 4rem;
}
.okk-page-title.color-blue {
  color: #90b5d1;
}
@media screen and (max-width: 767px) {
  .okk-page-title {
    font-size: 2rem;
    line-height: 1.5;
    margin-bottom: 2rem;
  }
}

.okk-headline .label {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  background: #90b5d1;
  color: #fff;
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 700;
  padding: 0.9rem 2rem;
  border-radius: 4rem;
  position: relative;
  margin-bottom: 1.8rem;
  letter-spacing: 0;
}
.okk-headline .label::after {
  position: absolute;
  content: "";
  z-index: 1;
  width: 2.3rem;
  height: 1.2rem;
  background: url("../img/common/ico_triang_ttl.png") no-repeat center center/cover;
  top: calc(100% - 0.4rem);
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  pointer-events: none;
}
.okk-headline .txt {
  display: block;
  font-size: 3.2rem;
  line-height: 1.4375;
  font-weight: 700;
  letter-spacing: 0;
}
.okk-headline + .okk-copy {
  margin-top: 2rem;
}
@media screen and (max-width: 767px) {
  .okk-headline .label {
    font-size: 1.4rem;
    line-height: 1.4285714286;
    padding: 1.1rem 2.2rem;
    margin-bottom: 1.8rem;
  }
  .okk-headline .txt {
    font-size: 2.6rem;
    line-height: 1.4230769231;
  }
}

.okk-sub-title {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #898989;
  text-align: center;
}
.okk-sub-title.color-base {
  color: #413e3e;
}
@media screen and (max-width: 767px) {
  .okk-sub-title {
    font-size: 1.8rem;
    line-height: 1.4444444444;
    margin-bottom: 1.6rem;
  }
}

.okk-heading-lv2 {
  font-size: 3.2rem;
  line-height: 1.4375;
  font-weight: 700;
  margin-bottom: 1rem;
}
@media screen and (max-width: 767px) {
  .okk-heading-lv2 {
    font-size: 2.4rem;
    line-height: 1.4583333333;
    margin-bottom: 0.8rem;
  }
}

.okk-heading-lv3 {
  font-size: 2.4rem;
  line-height: 1.5;
  font-weight: 700;
  margin-bottom: 3rem;
}
.okk-heading-lv3 .sm {
  font-size: 1.8rem;
  line-height: 1.6666666667;
}
@media screen and (max-width: 767px) {
  .okk-heading-lv3 {
    font-size: 1.8rem;
    line-height: 1.4444444444;
    margin-bottom: 1.6rem;
  }
  .okk-heading-lv3 .sm {
    font-size: 1.4rem;
    line-height: 1.8571428571;
  }
}

.okk-heading-lv4 {
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
  margin-bottom: 1rem;
}
@media screen and (max-width: 767px) {
  .okk-heading-lv4 {
    font-size: 1.4rem;
    line-height: 1.4285714286;
    margin-bottom: 0.8rem;
  }
}

.okk-copy {
  line-height: 2;
  font-weight: 500;
  letter-spacing: 0;
}
@media screen and (max-width: 767px) {
  .okk-copy {
    line-height: 1.85714286;
  }
}
@media screen and (max-width: 540px) {
  .okk-copy {
    text-align: left;
  }
}

.okk-list-dot {
  font-size: 1.4rem;
  line-height: 1.8571428571;
  font-weight: 500;
}
.okk-list-dot li {
  padding-left: 2.1rem;
  position: relative;
}
.okk-list-dot li::before {
  position: absolute;
  content: "●";
  top: 0;
  left: 0;
}
.okk-list-dot li:nth-child(n+2) {
  margin-top: 0.2rem;
}

.okk-note {
  font-size: 1.2rem;
  line-height: 1.5;
  font-weight: 500;
}
.okk-note li + li {
  margin-top: 0.2rem;
}

.okk-text-link {
  text-decoration: underline;
  text-underline-offset: 2px;
}
@media screen and (min-width: 768px) {
  .okk-text-link:hover {
    text-decoration: none;
  }
}

.okk-iframe-ytb {
  aspect-ratio: 16/9;
}
.okk-iframe-ytb iframe {
  display: block;
  width: 100%;
  height: 100%;
}

.slider-scrollbar-wrap {
  position: relative;
}

.slider-progress {
  height: 6.6rem;
  width: calc(100% - 18rem);
  display: block;
  position: relative;
}
.slider-progress::before {
  position: absolute;
  content: "";
  z-index: 1;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 100%;
  height: 0.4rem;
  border-radius: 1rem;
  overflow: hidden;
  background-color: #e2e2e2;
}
.okk-bg-gray .slider-progress::before {
  background-color: #fff;
}
.slider-progress__bar {
  position: absolute;
  z-index: 2;
  background: #90b5d1;
  height: 0.8rem;
  -webkit-transition: width 0.3s ease;
  transition: width 0.3s ease;
  border-radius: 1rem;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 0;
  color: transparent;
  text-indent: -9999rem;
}
@media screen and (max-width: 767px) {
  .slider-progress {
    height: 4.2rem;
    width: calc(100% - 12rem);
  }
}

.slider-scrollbar-list {
  padding-bottom: 4rem;
  margin-right: calc(50% - 50vw) !important;
  position: static;
}
.slider-scrollbar-list .slick-arrow {
  width: 6.6rem;
  height: 6.6rem;
  border: 2px solid #413e3e;
  background: #fff;
  border-radius: 50%;
  top: auto;
  bottom: 0;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}
.slider-scrollbar-list .slick-arrow::before {
  content: none;
}
.slider-scrollbar-list .slick-arrow::after {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  z-index: 1;
  width: 1.5rem;
  height: 1.1727272727rem;
  background: no-repeat center center/100% 100%;
}
.slider-scrollbar-list .slick-arrow.slick-prev {
  left: auto;
  right: calc(6.6rem + 1.4rem + 1.7rem);
}
.slider-scrollbar-list .slick-arrow.slick-prev::after {
  background-image: url("../img/common/ico_arrow_left.png");
  background-position: center left;
}
.slider-scrollbar-list .slick-arrow.slick-next {
  right: 1.7rem;
}
.slider-scrollbar-list .slick-arrow.slick-next::after {
  background-image: url("../img/common/ico_arrow_right.png");
  background-position: center right;
}
@media screen and (max-width: 767px) {
  .slider-scrollbar-list {
    padding-bottom: 3rem;
    margin-right: -2rem;
  }
  .slider-scrollbar-list .slick-arrow {
    width: 4.2rem;
    height: 4.2rem;
    border-width: 1px;
  }
  .slider-scrollbar-list .slick-arrow::after {
    width: 1.3rem;
    height: 1.01636364rem;
  }
  .slider-scrollbar-list .slick-arrow.slick-prev {
    right: calc(4.2rem + 1rem);
  }
  .slider-scrollbar-list .slick-arrow.slick-next {
    right: 0;
  }
}

.okk-t-kv {
  position: relative;
}
.okk-t-kv__desc {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-top: 3.8rem;
  pointer-events: none;
}
.okk-t-kv__desc::after {
  position: absolute;
  content: "";
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 30.6rem;
  background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, 0)), to(rgba(50, 50, 50, 0.73)));
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(50, 50, 50, 0.73));
  mix-blend-mode: multiply;
}
.okk-t-kv__title {
  position: relative;
  z-index: 2;
  color: #fff;
  font-family: "Noto Serif JP", serif;
  font-size: 4.4rem;
  line-height: 1.7272727273;
  font-weight: 600;
  letter-spacing: 0.1em;
  text-align: center;
  text-shadow: 0 0 6px rgba(0, 0, 0, 0.55);
}
.okk-t-kv__slogan {
  position: absolute;
  bottom: 13.6rem;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 2;
  color: #fff;
  background: rgba(144, 181, 209, 0.8);
  font-size: 1.6rem;
  line-height: 1.375;
  font-weight: 600;
  letter-spacing: 0;
  text-align: center;
  padding: 0.4rem 1rem 0.7rem;
}
.okk-t-kv__slider {
  margin: 0 !important;
}
.okk-t-kv .slide-item {
  width: 100vw;
  aspect-ratio: 1366/894;
}
@media screen and (min-height: 500px) {
  .okk-t-kv .slide-item {
    max-height: 100dvh;
  }
}
.okk-t-kv .slide-animation {
  -webkit-animation: fadezoom 12s 0s forwards;
          animation: fadezoom 12s 0s forwards;
}
.okk-t-kv .slick-dots {
  top: 50%;
  right: 50px;
  bottom: auto;
  width: auto;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  counter-reset: number;
}
.okk-t-kv .slick-dots li {
  display: block;
  position: relative;
  width: 40px;
  height: 40px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0);
  border-radius: 100%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.okk-t-kv .slick-dots li:before {
  counter-increment: number;
  content: counter(number, decimal-leading-zero);
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  color: #fff;
  font-size: 1.4rem;
}
.okk-t-kv .slick-dots li.slick-active {
  border: 1px solid rgba(255, 255, 255, 0.4);
}
.okk-t-kv .slick-dots li.slick-active .svg-elem-1 {
  -webkit-animation: animate-svg-stroke 8.5s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both, animate-svg-fill-1 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both;
          animation: animate-svg-stroke 8.5s cubic-bezier(0.47, 0, 0.745, 0.715) 0s both, animate-svg-fill-1 0.7s cubic-bezier(0.47, 0, 0.745, 0.715) 0.8s both;
}
.okk-t-kv .slick-dots li button:before {
  content: none;
}
.okk-t-kv .c-indicator__circle {
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  margin: auto;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  outline: none;
}
.okk-t-kv .c-indicator__circle circle {
  stroke-dasharray: 160;
  stroke-dashoffset: 160;
  fill: transparent;
}
@media screen and (max-width: 767px) {
  .okk-t-kv__desc {
    padding-top: 0;
    padding-bottom: 3.1rem;
  }
  .okk-t-kv__desc::after {
    height: 28.5rem;
    height: 76vw;
  }
  .okk-t-kv__title {
    font-size: 2.6rem;
    line-height: 1.4615384615;
  }
  .okk-t-kv__slogan {
    bottom: 4rem;
    font-size: 1.4rem;
    line-height: 1.5;
  }
  .okk-t-kv .slide-item {
    aspect-ratio: 375/450;
  }
  .okk-t-kv .slick-dots {
    bottom: 3rem;
    -webkit-transform: translateY(0);
            transform: translateY(0);
    right: 12px;
  }
  .okk-t-kv .slick-dots li {
    width: 32px;
    height: 32px;
    margin: 8px 0;
  }
  .okk-t-kv .slick-dots li:before {
    font-size: 1.2rem;
  }
  .okk-t-kv .c-indicator__circle {
    width: 32px;
    height: 32px;
  }
}

.okk-t-links {
  position: relative;
  z-index: 2;
  margin-top: -9.7rem;
  padding-bottom: 6rem;
}
.okk-t-links__inner {
  max-width: 96.6rem;
  margin: 0 auto;
}
.okk-t-links__box {
  background: #fff;
  border-radius: 2.5rem;
  padding: 2rem 1rem;
}
.okk-t-links__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.okk-t-links__item {
  width: 31.5rem;
  max-width: 33.333333%;
  padding: 0 1rem;
}
.okk-t-links__link {
  padding-bottom: 0.8rem;
  display: block;
}
.okk-t-links__link .links-img {
  aspect-ratio: 295/190;
  border-radius: 1.2rem;
  margin-bottom: 1rem;
}
.okk-t-links__link .links-copy {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 500;
  text-decoration: underline;
}
@media screen and (min-width: 768px) {
  .okk-t-links__link {
    -webkit-transition: 0.25s;
    transition: 0.25s;
  }
  .okk-t-links__link:hover {
    opacity: 0.75;
  }
  .okk-t-links__link:hover .links-copy {
    text-decoration: none;
  }
}
.okk-t-links .slick-arrow {
  width: 4rem;
  height: 4rem;
  background: #fff no-repeat center center/1.208rem 0.843rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
  z-index: 2;
}
.okk-t-links .slick-arrow.slick-prev {
  left: -4rem;
  background-image: url("../img/common/ico_arrow_left.png");
}
.okk-t-links .slick-arrow.slick-next {
  right: -4rem;
  background-image: url("../img/common/ico_arrow_right.png");
}
.okk-t-links .slick-arrow::before {
  content: none;
}
@media screen and (max-width: 767px) {
  .okk-t-links {
    margin-top: 0;
    padding: 2rem 0 3rem;
    padding: 2rem 0;
  }
  .okk-t-links .okk-inner {
    padding: 0;
  }
  .okk-t-links__box {
    padding: 1rem 0;
  }
  .okk-t-links__item {
    width: 30rem;
    max-width: 30rem;
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-links__item {
    width: 17.8rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-links__link {
    padding-bottom: 0.8rem;
    display: block;
  }
  .okk-t-links__link .links-img {
    aspect-ratio: 158/105;
    margin-bottom: 0.8rem;
  }
  .okk-t-links__link .links-copy {
    font-size: 1.2rem;
    line-height: 1.5;
  }
  .okk-t-links .slick-arrow {
    top: 7rem;
    -webkit-transform: translateY(0);
            transform: translateY(0);
    bottom: auto;
  }
  .okk-t-links .slick-arrow.slick-prev {
    left: calc(50vw - 14rem - 1rem - 4rem);
  }
  .okk-t-links .slick-arrow.slick-next {
    right: calc(50vw - 14rem - 1rem - 4rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-links .slick-arrow {
    top: 3rem;
  }
  .okk-t-links .slick-arrow.slick-prev {
    left: calc(50vw - 7.9rem - 1rem - 4rem);
  }
  .okk-t-links .slick-arrow.slick-next {
    right: calc(50vw - 7.9rem - 1rem - 4rem);
  }
}

.okk-t-point {
  overflow: hidden;
}
.okk-t-point__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: -1rem 0 0 -1rem;
}
.okk-t-point__item {
  width: 29rem;
  padding: 1rem;
}
.okk-t-point .point-img {
  aspect-ratio: 270/164;
}
.okk-t-point .point-desc {
  padding: 2.4rem 1.2rem 2.8rem;
}
.okk-t-point .point-ttl {
  font-weight: 700;
  padding-right: 4rem;
}
.okk-t-point__link {
  background: #fff;
  border-radius: 1.2rem;
  overflow: hidden;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  display: block;
  height: 100%;
  position: relative;
}
.okk-t-point__link::after {
  position: absolute;
  content: "";
  z-index: 1;
  width: 4rem;
  height: 4rem;
  bottom: 3rem;
  right: 1.2rem;
  background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center/1.208rem 0.843rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
}
@media screen and (max-width: 767px) {
  .okk-t-point__list {
    margin: -1rem 0 -1rem -1rem;
  }
  .okk-t-point__item {
    width: 25.7rem;
    padding: 1rem;
  }
  .okk-t-point .point-img {
    aspect-ratio: 237/131;
  }
  .okk-t-point .point-desc {
    padding: 1.4rem 1.2rem 1.8rem;
  }
  .okk-t-point__link::after {
    width: 3.2rem;
    height: 3.2rem;
    bottom: 2.4rem;
    background-size: 1rem 0.6978476821rem;
  }
}

.okk-t-movie {
  margin-top: 6rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-t-movie__item {
  width: 46rem;
  max-width: 100%;
}
.okk-t-movie__btn {
  display: block;
  position: relative;
  overflow: hidden;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 2rem;
}
.okk-t-movie__btn .moive-bg {
  width: 100%;
  height: 13.4rem;
  position: relative;
}
.okk-t-movie__btn .moive-bg::after {
  position: absolute;
  inset: 0;
  content: "";
  z-index: 1;
  background-image: -webkit-gradient(linear, left top, right top, from(rgba(65, 62, 62, 0)), to(rgba(65, 62, 62, 0.54)));
  background-image: linear-gradient(to right, rgba(65, 62, 62, 0), rgba(65, 62, 62, 0.54));
  mix-blend-mode: multiply;
}
.okk-t-movie__btn .movie-desc {
  position: absolute;
  inset: 0.4rem;
  border: 2px solid #fff;
  border-radius: 1.8rem;
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-t-movie__btn .moive-ico {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 5.7rem;
  height: 5.7rem;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 50%;
}
.okk-t-movie__btn .movie-ttl {
  margin-left: auto;
  margin-right: 2rem;
  min-width: 15.2rem;
  text-align: center;
  color: #fff;
}
.okk-t-movie__btn .movie-ttl p {
  line-height: 2.375;
}
.okk-t-movie__btn .movie-ttl h3 {
  border-top: 1px solid #fff;
  font-size: 2rem;
  line-height: 1.8;
}
.okk-t-movie:first-child {
  margin-top: 0 !important;
}
@media screen and (max-width: 767px) {
  .okk-t-movie {
    margin-top: 3rem;
  }
  .okk-t-movie__item {
    width: 33.5rem;
  }
  .okk-t-movie__btn {
    border-radius: 1.8rem;
  }
  .okk-t-movie__btn .moive-bg {
    height: 12rem;
  }
  .okk-t-movie__btn .movie-desc {
    inset: 0.3rem;
    border-radius: 1.6rem;
  }
  .okk-t-movie__btn .moive-ico {
    width: 4.2rem;
    height: 4.2rem;
  }
  .okk-t-movie__btn .movie-ttl {
    margin-right: 1.2rem;
    min-width: 11.9rem;
  }
  .okk-t-movie__btn .movie-ttl p {
    font-size: 1.2rem;
    line-height: 2.3333333333;
  }
  .okk-t-movie__btn .movie-ttl h3 {
    font-size: 1.6rem;
    line-height: 1.75;
  }
}

.okk-video-popup {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-video-popup__content {
  position: relative;
  margin: 4rem auto;
  padding: 4rem;
  width: 106rem;
  max-width: 94%;
  background: #f2f5f7;
  border-radius: 2rem;
}
.okk-video-popup .close-btn {
  position: absolute;
  top: 0;
  right: 1rem;
  color: #413e3e;
  font-size: 3rem;
  font-weight: bold;
  cursor: pointer;
}
.okk-video-popup iframe {
  aspect-ratio: 16/9;
  display: block;
  border-radius: 1.2rem;
  overflow: hidden;
}
.okk-video-popup.is-active {
  opacity: 1;
  visibility: visible;
}
@media screen and (max-width: 767px) {
  .okk-video-popup__content {
    margin: 0.4rem auto;
    padding: 0.4rem;
    border-radius: 1.2rem;
  }
  .okk-video-popup .close-btn {
    top: -3.5rem;
    right: 0;
    line-height: 1;
    color: #fff;
  }
  .okk-video-popup iframe {
    border-radius: 1.2rem;
  }
}

.okk-t-rentalplan {
  padding-bottom: 9rem;
  overflow: hidden;
}
.okk-t-rentalplan__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: -1rem 0 0 -1.9rem;
}
.okk-t-rentalplan__item {
  width: 36rem;
  padding: 1rem 1.9rem 0.6rem;
}
.okk-t-rentalplan__link {
  display: block;
  height: 100%;
  position: relative;
}
.okk-t-rentalplan__link .rentalplan-no {
  position: absolute;
  z-index: 2;
  top: -0.9rem;
  left: -2.3rem;
  left: -1.9rem;
  border-radius: 50%;
  background: #413e3e;
  color: #fff;
  width: 7.5rem;
  height: 7.5rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 1.25;
  font-weight: 700;
  text-align: center;
}
.okk-t-rentalplan__link .rentalplan-img {
  position: relative;
}
.okk-t-rentalplan__link .rentalplan-img figure {
  aspect-ratio: 360/406;
  border-radius: 2.5rem;
}
.okk-t-rentalplan__link .rentalplan-tags {
  position: absolute;
  bottom: 1.3rem;
  left: 1.3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 0.4rem;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-t-rentalplan__link .rentalplan-tags li {
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 500;
  border-radius: 2rem;
  background-color: #fff;
  padding: 0.7rem 0.9rem;
  display: inline-block;
}
.okk-t-rentalplan__link .rentalplan-desc {
  padding: 1.2rem 0 3rem;
}
.okk-t-rentalplan__link .rentalplan-ttl {
  font-size: 3.6rem;
  line-height: 1.4444444444;
  font-weight: 700;
}
.okk-t-rentalplan__link .rentalplan-price {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 2.6rem;
  line-height: 1.4230769231;
  font-weight: 700;
  gap: 0.6rem;
}
.okk-t-rentalplan__link .rentalplan-price .price-old {
  margin-top: 0.3rem;
  color: #868686;
  font-size: 1.8rem;
  line-height: 1.6666666667;
  padding-right: 1.7rem;
  position: relative;
}
.okk-t-rentalplan__link .rentalplan-price .price-old::after {
  position: absolute;
  content: "";
  z-index: 1;
  background: url("../img/common/ico_arrow_left_gray.png") no-repeat right center/cover;
  width: 0.65rem;
  height: 1.1rem;
  top: 55%;
  right: 0.3rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.okk-t-rentalplan__link .rentalplan-price .tax {
  margin-top: 0.5rem;
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 400;
}
.okk-t-rentalplan__link .rentalplan-copy {
  margin-top: 2rem;
  font-size: 1.4rem;
  line-height: 1.8571428571;
  font-weight: 400;
}
.okk-t-rentalplan__link .rentalplan-btn {
  max-width: 32rem;
  margin: 0 auto;
}
.okk-t-rentalplan__link .rentalplan-btn .okk-btn {
  padding-top: 1.6rem;
  padding-bottom: 1.6rem;
}
@media screen and (max-width: 767px) {
  .okk-t-rentalplan {
    padding-bottom: 6rem;
  }
  .okk-t-rentalplan__list {
    margin: -1.3rem 0 1rem -1.3rem;
  }
  .okk-t-rentalplan__item {
    width: 29.6rem;
    padding: 1.3rem 1.3rem 0;
  }
  .okk-t-rentalplan__link .rentalplan-no {
    top: -1.3rem;
    left: -1.3rem;
    width: 6rem;
    height: 6rem;
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
  .okk-t-rentalplan__link .rentalplan-img figure {
    aspect-ratio: 270/290;
    border-radius: 1.8rem;
  }
  .okk-t-rentalplan__link .rentalplan-tags {
    bottom: 1rem;
    left: 1rem;
    gap: 0.6rem;
  }
  .okk-t-rentalplan__link .rentalplan-desc {
    padding: 1.2rem 0 2rem;
  }
  .okk-t-rentalplan__link .rentalplan-ttl {
    font-size: 2.4rem;
    line-height: 1.4583333333;
  }
  .okk-t-rentalplan__link .rentalplan-price {
    font-size: 2rem;
    line-height: 1.45;
    gap: 0.4rem;
  }
  .okk-t-rentalplan__link .rentalplan-price .price-old {
    margin-top: 0.3rem;
    font-size: 1.4rem;
    line-height: 1.4285714286;
    padding-right: 1.8rem;
  }
  .okk-t-rentalplan__link .rentalplan-price .tax {
    margin-top: 0.3rem;
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
  .okk-t-rentalplan__link .rentalplan-copy {
    margin-top: 1rem;
  }
  .okk-t-rentalplan__link .rentalplan-btn {
    max-width: 25rem;
  }
  .okk-t-rentalplan__link .rentalplan-btn .okk-btn {
    padding-top: 1.3rem;
    padding-bottom: 1.3rem;
    font-size: 1.4rem;
    line-height: 1.4285714286;
  }
}

.okk-ls-services {
  margin-top: 6rem;
  background: #f8f8f8;
  padding: 4.2rem;
  border-radius: 2rem;
}
.okk-ls-services .okk-section-head {
  margin-bottom: 2rem;
}
.okk-ls-services__ttl {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #898989;
}
.okk-ls-services__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2rem;
}
.okk-ls-services__item {
  width: calc((100% - 4rem) / 3);
}
.okk-ls-services__item__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 2rem;
  background: #fff;
  border-radius: 1.2rem;
  height: 100%;
}
.okk-ls-services__ico {
  width: 6.1rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-ls-services__desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding-left: 2rem;
}
.okk-ls-services__hdg {
  margin-bottom: 0.8rem;
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
}
.okk-ls-services__copy {
  font-size: 1.4rem;
  line-height: 1.7142857143;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  .okk-ls-services {
    margin-top: 5rem;
    padding: 2rem;
    border-radius: 1.2rem;
  }
  .okk-ls-services__ttl {
    font-size: 1.8rem;
    line-height: 1.4444444444;
    margin-bottom: 1.6rem;
  }
  .okk-ls-services__list {
    gap: 1rem;
  }
  .okk-ls-services__item {
    width: calc(50% - 0.5rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-ls-services__item {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-ls-services__item__inner {
    padding: 1.2rem 1rem 1.4rem 2rem;
    border-radius: 1rem;
    height: auto;
  }
  .okk-ls-services__ico {
    width: 3.3rem;
  }
  .okk-ls-services__desc {
    padding-left: 1.4rem;
  }
  .okk-ls-services__hdg {
    margin-bottom: 0.3rem;
    font-size: 1.6rem;
    line-height: 1.5;
  }
  .okk-ls-services__copy {
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
}

.okk-t-hairset__photo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 2rem;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-t-hairset__photo li {
  width: calc((100% - 6rem) / 4);
  aspect-ratio: 274/253;
  border-radius: 1.2rem;
}
.okk-t-hairset__desc {
  margin-top: 4rem;
  border-radius: 2rem;
  background: #fff;
  padding: 4rem;
}
.okk-t-hairset__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2.2rem;
}
.okk-t-hairset__item {
  width: calc((100% - 4.4rem) / 3);
  background: #f8f8f8;
  border-radius: 1.2rem;
}
.okk-t-hairset__item__inner {
  padding: 2rem;
}
.okk-t-hairset .hairset-ttl {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  color: #d98794;
}
.okk-t-hairset .hairset-price {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.6rem;
}
.okk-t-hairset .hairset-price .tax {
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 400;
  margin-top: 0.4rem;
}
@media screen and (max-width: 767px) {
  .okk-t-hairset__photo {
    gap: 1.1rem;
  }
  .okk-t-hairset__photo li {
    width: calc(50% - 0.55rem);
    aspect-ratio: 162/148;
    border-radius: 1rem;
  }
  .okk-t-hairset__desc {
    margin-top: 4rem;
    border-radius: 1.2rem;
    padding: 2rem;
  }
  .okk-t-hairset__row {
    gap: 1rem;
  }
  .okk-t-hairset__item {
    border-radius: 1rem;
    width: calc(50% - 0.5rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-hairset__item {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-hairset__item__inner {
    padding: 1.4rem 2rem 1.3rem;
  }
  .okk-t-hairset .hairset-ttl {
    font-size: 1.6rem;
    line-height: 1.5;
  }
  .okk-t-hairset .hairset-price {
    font-size: 2rem;
    line-height: 1.45;
    gap: 1rem;
  }
  .okk-t-hairset .hairset-price .tax {
    font-size: 1.2rem;
    line-height: 1.4166666667;
    margin-top: 0.3rem;
  }
}

.okk-slider-gallery__photo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.okk-slider-gallery__photo .slider-item {
  width: 47.5rem;
  padding: 0 1rem;
}
.okk-slider-gallery__photo figure {
  aspect-ratio: 455/419;
  border-radius: 2.5rem;
}
@media screen and (max-width: 767px) {
  .okk-slider-gallery__photo .slider-item {
    width: 21rem;
    padding: 0 1rem;
  }
  .okk-slider-gallery__photo figure {
    aspect-ratio: 196/180;
    border-radius: 1.8rem;
  }
}

.okk-t-returnplan__desc {
  border-radius: 2rem;
  background: #fff;
  padding: 4rem;
}
.okk-t-returnplan__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2.2rem;
}
.okk-t-returnplan__item {
  width: calc((100% - 4.4rem) / 3);
  background: #f8f8f8;
  border-radius: 1.2rem;
}
.okk-t-returnplan__item__inner {
  padding: 2rem;
}
.okk-t-returnplan .returnplan-ttl {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  color: #d98794;
}
.okk-t-returnplan .returnplan-price {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.6rem;
}
.okk-t-returnplan .returnplan-price .tax {
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 400;
  margin-top: 0.4rem;
}
@media screen and (max-width: 767px) {
  .okk-t-returnplan__desc {
    border-radius: 1.2rem;
    padding: 2rem;
  }
  .okk-t-returnplan__row {
    gap: 1rem;
  }
  .okk-t-returnplan__item {
    border-radius: 1rem;
    width: calc(50% - 0.5rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-returnplan__item {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-returnplan__item__inner {
    padding: 1.4rem 2rem 1.3rem;
  }
  .okk-t-returnplan .returnplan-ttl {
    font-size: 1.6rem;
    line-height: 1.5;
  }
  .okk-t-returnplan .returnplan-price {
    font-size: 2rem;
    line-height: 1.45;
    gap: 1rem;
  }
  .okk-t-returnplan .returnplan-price .tax {
    font-size: 1.2rem;
    line-height: 1.4166666667;
    margin-top: 0.3rem;
  }
}

.okk-ls-storeinfo {
  padding: 6rem 0;
}
.okk-ls-storeinfo__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  gap: 3rem;
}
.okk-ls-storeinfo__item {
  width: calc((100% - 9rem) / 4);
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 1.2rem;
  background: #fff;
  overflow: hidden;
}
@media screen and (min-width: 768px) {
  .okk-ls-storeinfo__item {
    min-width: 26.6rem;
  }
}
.okk-ls-storeinfo__link {
  display: block;
}
.okk-ls-storeinfo__link .storeinfo-img {
  aspect-ratio: 274/180;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
}
.okk-ls-storeinfo__link .storeinfo-txt {
  margin-top: 1.4rem;
  margin-bottom: 1.4rem;
  text-align: center;
  font-size: 2rem;
  line-height: 1.4;
  font-weight: 700;
}
.okk-ls-storeinfo__link .storeinfo-txt .fz-sm {
  font-size: 1.6rem;
  line-height: 1.5625;
}
.okk-ls-storeinfo__link .storeinfo-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-ls-storeinfo__link .okk-btn {
  font-size: 1.2rem;
  line-height: 1.3333333333;
  padding: 0.5rem 2.5rem;
  width: 20.8rem;
}
.okk-ls-storeinfo__link .okk-btn::after {
  right: 1.4rem;
  width: 1rem;
  height: 0.782rem;
}
.okk-ls-storeinfo .storeinfo-description {
  padding: 1.8rem 1.4rem;
  font-size: 1.2rem;
  line-height: 1.6666666667;
}
.okk-ls-storeinfo .storeinfo-description p {
  color: #898989;
  margin-bottom: 0.5rem;
}
.okk-ls-storeinfo .storeinfo-features dt {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  text-decoration: underline;
}
@media (hover: hover) {
  .okk-ls-storeinfo .storeinfo-features dt:hover {
    text-decoration: none;
  }
}
.okk-ls-storeinfo .storeinfo-features dt::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1.4rem;
  height: 1.4rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
}
.okk-ls-storeinfo .storeinfo-features dt span {
  padding-right: 1.7rem;
  position: relative;
  text-underline-offset: 2px;
}
.okk-ls-storeinfo .storeinfo-features dt span::before, .okk-ls-storeinfo .storeinfo-features dt span::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0.4rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 0.6rem;
  height: 1px;
  background: #413e3e;
}
.okk-ls-storeinfo .storeinfo-features dt span::after {
  opacity: 1;
  -webkit-transition: 0.25s;
  transition: 0.25s;
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
.okk-ls-storeinfo .storeinfo-features dt.is-active span::after {
  opacity: 0;
}
.okk-ls-storeinfo .storeinfo-features dd {
  margin-top: 1.2rem;
  background: #faf2f4;
  padding: 1.3rem 1rem;
  border-radius: 0.4rem;
}
@media screen and (max-width: 767px) {
  .okk-ls-storeinfo {
    padding-top: 5rem;
  }
  .okk-ls-storeinfo__list {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 1.8rem;
  }
  .okk-ls-storeinfo__item {
    width: 100%;
  }
  .okk-ls-storeinfo__link {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: flex-end;
    gap: 1.4rem;
    padding: 1.6rem 1.2rem 0;
  }
  .okk-ls-storeinfo__link .storeinfo-img {
    width: 16rem;
    max-width: 50%;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    aspect-ratio: 160/114;
    border-radius: 0.8rem;
    overflow: hidden;
  }
  .okk-ls-storeinfo__link .storeinfo-link-info {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
  .okk-ls-storeinfo__link .storeinfo-txt {
    margin-top: 0;
    margin-bottom: 1.4rem;
    text-align: left;
    font-size: 1.6rem;
    line-height: 1.5;
  }
  .okk-ls-storeinfo__link .storeinfo-txt .fz-sm {
    font-size: 1.2rem;
    line-height: 2;
  }
  .okk-ls-storeinfo__link .storeinfo-btn {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .okk-ls-storeinfo__link .okk-btn {
    padding: 0.4rem 2rem;
    width: 13.7rem;
    max-width: 100%;
  }
  .okk-ls-storeinfo__link .okk-btn::after {
    right: 1.2rem;
  }
  .okk-ls-storeinfo .storeinfo-description {
    padding: 1rem 1.2rem 1.6rem;
    font-size: 1.2rem;
    line-height: 1.6666666667;
  }
  .okk-ls-storeinfo .storeinfo-description p {
    color: #898989;
    margin-bottom: 0.4rem;
  }
  .okk-ls-storeinfo .storeinfo-features {
    padding-top: 0.6rem;
  }
  .okk-ls-storeinfo .storeinfo-features dd {
    padding: 1.2rem 1.2rem;
  }
}

.okk-store-other {
  padding: 8rem 0;
  overflow: hidden;
}
.okk-store-other__box {
  margin-top: 0;
  max-width: 96.6rem;
  margin: 0 auto;
  background: #fff;
  border-radius: 2.5rem;
  padding: 2rem 1rem;
}
@media screen and (min-width: 768px) {
  .okk-store-other__box {
    background: #f2f5f7;
  }
  .okk-bg-gray .okk-store-other__box {
    background: #fff;
  }
}
.okk-store-other__list {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.okk-store-other__item {
  width: 31.5rem;
  max-width: 33.333333%;
  padding: 0 1rem;
}
.okk-store-other__link {
  padding-bottom: 0.8rem;
  display: block;
}
.okk-store-other__link .img {
  aspect-ratio: 295/190;
  border-radius: 1.2rem;
  margin-bottom: 1rem;
}
.okk-store-other__link .txt {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 500;
  text-decoration: underline;
}
@media screen and (min-width: 768px) {
  .okk-store-other__link {
    -webkit-transition: 0.25s;
    transition: 0.25s;
  }
  .okk-store-other__link:hover {
    opacity: 0.75;
  }
  .okk-store-other__link:hover .txt {
    text-decoration: none;
  }
}
.okk-store-other .slick-arrow {
  width: 4rem;
  height: 4rem;
  background: #fff no-repeat center center/1.208rem 0.843rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
  z-index: 2;
}
.okk-store-other .slick-arrow.slick-prev {
  left: -4rem;
  background-image: url("../img/common/ico_arrow_left.png");
}
.okk-store-other .slick-arrow.slick-next {
  right: -4rem;
  background-image: url("../img/common/ico_arrow_right.png");
}
.okk-store-other .slick-arrow::before {
  content: none;
}
@media screen and (max-width: 767px) {
  .okk-store-other {
    padding: 4rem 0;
  }
  .okk-store-other__box {
    margin: 0 -2rem;
    padding: 0;
    background: transparent;
  }
  .okk-store-other__item {
    width: 30rem;
    max-width: 30rem;
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-store-other__item {
    width: 17.8rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-store-other__link {
    padding-bottom: 0;
    display: block;
  }
  .okk-store-other__link .storeinfo-other-img {
    aspect-ratio: 158/105;
    margin-bottom: 0.8rem;
  }
  .okk-store-other__link .storeinfo-other-txt {
    font-size: 1.2rem;
    line-height: 1.5;
  }
  .okk-store-other .slick-arrow {
    top: 7rem;
    -webkit-transform: translateY(0);
            transform: translateY(0);
    bottom: auto;
  }
  .okk-store-other .slick-arrow.slick-prev {
    left: calc(50vw - 14rem - 1rem - 4rem);
  }
  .okk-store-other .slick-arrow.slick-next {
    right: calc(50vw - 14rem - 1rem - 4rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-store-other .slick-arrow {
    top: 3rem;
  }
  .okk-store-other .slick-arrow.slick-prev {
    left: calc(50vw - 7.9rem - 1rem - 4rem);
  }
  .okk-store-other .slick-arrow.slick-next {
    right: calc(50vw - 7.9rem - 1rem - 4rem);
  }
}
.okk-t-store-other .okk-store-other {
  padding-top: 2rem;
}

.okk-t-about__inner {
  position: relative;
}
.okk-t-about__content {
  padding: 6rem 28rem;
  position: relative;
  z-index: 2;
}
.okk-t-about__img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  list-style: none;
}
.okk-t-about__img li {
  width: 26.4rem;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.okk-t-about__img li span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: repeat-y;
  background-size: 100% 125.3rem;
  z-index: 1;
}
.okk-t-about__img li:nth-child(1) span {
  -webkit-animation: 25s linear infinite scrollAboutDown;
          animation: 25s linear infinite scrollAboutDown;
  background-image: url("../img/home/<USER>");
}
.okk-t-about__img li:nth-child(2) span {
  -webkit-animation: 25s linear infinite scrollAboutUp;
          animation: 25s linear infinite scrollAboutUp;
  background-image: url("../img/home/<USER>");
}
@media screen and (max-width: 767px) {
  .okk-t-about {
    padding: 4rem 0;
  }
  .okk-t-about .okk-copy {
    text-align: center;
  }
  .okk-t-about__content {
    padding: 16.3rem 0;
  }
  .okk-t-about__img {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin: 0 -2rem;
    width: calc(100% + 4rem);
  }
  .okk-t-about__img li {
    width: 100%;
    height: 13.3rem;
  }
  .okk-t-about__img li span {
    background-repeat: repeat-x;
    background-size: 68.7rem 100%;
  }
  .okk-t-about__img li:nth-child(1) span {
    -webkit-animation: 25s linear infinite scrollAboutRight;
            animation: 25s linear infinite scrollAboutRight;
    background-image: url("../img/home/<USER>");
  }
  .okk-t-about__img li:nth-child(2) span {
    -webkit-animation: 25s linear infinite scrollAboutLeft;
            animation: 25s linear infinite scrollAboutLeft;
    background-image: url("../img/home/<USER>");
  }
}

.okk-t-column__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 3.8rem;
}
.okk-t-column__item {
  width: calc((100% - 7.6rem) / 3);
}
.okk-t-column__link .column-img {
  aspect-ratio: 360/270;
  border-radius: 1.2rem;
}
.okk-t-column__link .column-cat {
  margin-top: 1.4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 0.6rem;
}
.okk-t-column__link .column-cat li {
  border: 1px solid #d98794;
  background: #fff;
  color: #d98794;
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 500;
  padding: 0.6rem 0.8rem;
  border-radius: 3.1rem;
}
.okk-t-column__link .column-txt {
  margin-top: 1rem;
  line-height: 1.75;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  .okk-t-column__list {
    gap: 2rem;
  }
  .okk-t-column__item {
    width: calc(50% - 1rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-column__item {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-column__link .column-img {
    aspect-ratio: 335/200;
  }
  .okk-t-column__link .column-txt {
    margin-top: 0.8rem;
    font-size: 1.6rem;
    line-height: 1.5;
    text-decoration: underline;
  }
}

.okk-t-formal__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-t-formal__item {
  width: 46rem;
}
.okk-t-formal__btn {
  display: block;
  position: relative;
  overflow: hidden;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 2rem;
}
.okk-t-formal__btn .formal-bg {
  width: 100%;
  height: 13.4rem;
  position: relative;
}
.okk-t-formal__btn .formal-bg::after {
  position: absolute;
  inset: 0;
  content: "";
  z-index: 1;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(65, 62, 62, 0)), to(rgba(65, 62, 62, 0.54)));
  background-image: linear-gradient(to bottom, rgba(65, 62, 62, 0), rgba(65, 62, 62, 0.54));
  mix-blend-mode: multiply;
}
.okk-t-formal__btn .formal-ico {
  position: absolute;
  z-index: 1;
  width: 4rem;
  height: 4rem;
  bottom: 1.6rem;
  right: 1.6rem;
  background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center/1.208rem 0.843rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
}
.okk-t-formal__btn .formal-desc {
  position: absolute;
  inset: 0.4rem;
  border: 2px solid #fff;
  border-radius: 1.8rem;
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.okk-t-formal__btn .formal-txt {
  text-align: center;
  color: #fff;
  width: 100%;
  padding-bottom: 1.5rem;
}
.okk-t-formal__btn .formal-txt p {
  line-height: 1.5;
}
.okk-t-formal__btn .formal-txt h3 {
  font-size: 2rem;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  .okk-t-formal__item {
    width: 33.5rem;
    max-width: 100%;
  }
  .okk-t-formal__btn {
    border-radius: 1.8rem;
  }
  .okk-t-formal__btn .formal-bg {
    height: 12rem;
  }
  .okk-t-formal__btn .formal-ico {
    width: 3.2rem;
    height: 3.2rem;
    bottom: 1.2rem;
    right: 1.2rem;
    background-size: 1rem 0.6978476821rem;
  }
  .okk-t-formal__btn .formal-desc {
    inset: 0.3rem;
    border: 2px solid #fff;
    border-radius: 1.6rem;
  }
  .okk-t-formal__btn .formal-txt {
    padding-bottom: 1.2rem;
  }
  .okk-t-formal__btn .formal-txt p {
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
  .okk-t-formal__btn .formal-txt h3 {
    font-size: 1.4rem;
    line-height: 1.4285714286;
  }
}

.okk-t-publication__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 3.8rem;
}
.okk-t-publication__item {
  width: calc((100% - 7.6rem) / 3);
}
.okk-t-publication__link .publication-img {
  aspect-ratio: 360/270;
  border-radius: 1.2rem;
}
.okk-t-publication__link .publication-time {
  margin-top: 2rem;
  color: #d98794;
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 400;
}
.okk-t-publication__link .publication-txt {
  margin-top: 1rem;
  line-height: 1.75;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  .okk-t-publication__list {
    gap: 3rem 2rem;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .okk-t-publication__item {
    width: calc(50% - 1rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-publication__item {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-publication__link .publication-img {
    aspect-ratio: 335/200;
  }
  .okk-t-publication__link .publication-time {
    margin-top: 1.4rem;
  }
  .okk-t-publication__link .publication-txt {
    margin-top: 0.8rem;
    font-size: 1.6rem;
    line-height: 1.5;
    text-decoration: underline;
  }
}

.okk-t-cover-woman {
  padding-top: 6rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.okk-t-cover-woman__item {
  width: 47.8rem;
}
.okk-t-cover-woman__bnr {
  border-radius: 1.2rem;
  display: block;
}
@media screen and (max-width: 767px) {
  .okk-t-cover-woman {
    padding-top: 3.6rem;
  }
  .okk-t-cover-woman__item {
    width: 35.1rem;
    max-width: calc(100% + 1.6rem);
    margin: 0 -0.8rem;
  }
}

.okk-t-news {
  background: #fffbf2;
}
.okk-t-news__content {
  max-width: 96.5rem;
  margin: 0 auto;
}
.okk-t-news__list {
  border-top: 1px solid #c3c3c3;
}
.okk-t-news__item {
  border-bottom: 1px solid #c3c3c3;
}
.okk-t-news__link {
  padding: 2.5rem 0;
  display: block;
}
@media screen and (min-width: 768px) {
  .okk-t-news__link:hover .news-desc {
    text-decoration: underline;
  }
}
.okk-t-news__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-t-news .news-time {
  width: 17.7rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  font-weight: 700;
  color: #d98794;
  padding-left: 1rem;
}
.okk-t-news .news-desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  font-weight: 400;
}
@media screen and (max-width: 767px) {
  .okk-t-news__link {
    padding: 1.5rem 0;
  }
  .okk-t-news__inner {
    display: block;
  }
  .okk-t-news .news-time {
    width: 100%;
    padding-left: 0;
  }
  .okk-t-news .news-desc {
    margin-top: 0.8rem;
    text-decoration: underline;
  }
}

.okk-t-corporate {
  padding-bottom: 9rem;
}
.okk-t-corporate .okk-section-head {
  margin-bottom: 2rem;
}
.okk-t-corporate .okk-headline .txt {
  font-size: 2.6rem;
}
.okk-t-corporate__content {
  max-width: 96.6rem;
  margin: 0 auto;
  border-radius: 2.5rem;
  background: #f8f8f8;
  padding: 4rem;
}
.okk-t-corporate__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 3rem 4rem;
}
.okk-t-corporate__item {
  width: 41.2rem;
  max-width: calc(50% - 2rem);
}
.okk-t-corporate__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1.2rem;
}
.okk-t-corporate__img {
  aspect-ratio: 1/1;
  border-radius: 0.6rem;
  width: 10.6rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-t-corporate__desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding-right: 3rem;
}
.okk-t-corporate__copy {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 400;
}
.okk-t-corporate__ttl {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  margin-top: 0.5rem;
}
.okk-t-corporate__link {
  padding: 0.7rem;
  display: block;
  background: #fff;
  border-radius: 1.2rem;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  position: relative;
}
.okk-t-corporate__link::after {
  position: absolute;
  top: 50%;
  right: 2rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
  content: "";
  background: no-repeat center center/100% 100%;
  width: 1.9rem;
  height: 1.9rem;
}
.okk-t-corporate__link[target=_blank]::after {
  background-image: url("../img/common/ico_extend.png");
}
@media screen and (max-width: 767px) {
  .okk-t-corporate {
    padding-bottom: 6rem;
  }
  .okk-t-corporate .okk-headline .txt {
    font-size: 1.8rem;
  }
  .okk-t-corporate__content {
    padding: 2rem;
    border-radius: 1.2rem;
  }
  .okk-t-corporate__list {
    gap: 1rem;
  }
  .okk-t-corporate__item {
    max-width: calc(50% - 0.5rem);
  }
}
@media screen and (max-width: 767px) and (max-width: 540px) {
  .okk-t-corporate__item {
    max-width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .okk-t-corporate__inner {
    gap: 1rem;
  }
  .okk-t-corporate__img {
    width: 6.6rem;
  }
  .okk-t-corporate__desc {
    padding-right: 1.6rem;
  }
  .okk-t-corporate__copy {
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
  .okk-t-corporate__ttl {
    font-size: 1.8rem;
    line-height: 1.4444444444;
    margin-top: 0.3rem;
  }
  .okk-t-corporate__link {
    border-radius: 1rem;
  }
  .okk-t-corporate__link::after {
    right: 1rem;
    width: 1.4rem;
    height: 1.4rem;
  }
}

.link-pages {
  margin-top: 4rem;
  border-top: 1px solid #c1c1c1;
  border-bottom: 1px solid #c1c1c1;
  padding: 3rem 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 2rem;
}
.link-pages > a {
  color: #999999;
  font-size: 1.6rem;
  line-height: 1.75;
  padding: 0.5rem;
  width: 4rem;
  border: 1px solid #c1c1c1;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-color: #fff;
}
.link-pages > a.active {
  background-color: #d98794;
  color: #fff;
  border-color: #d98794;
}
.link-pages > a:hover {
  border-color: #d98794;
}
.link-pages .nav {
  width: auto;
  padding: 0.5rem 3rem;
  border-radius: 2rem;
}
.link-pages .nav.prev {
  padding: 0.5rem 2.3rem 0.5rem 3.9rem;
  background: url("/common/img/arr_gray01.png") no-repeat left 1.4rem center #fff;
  background-size: 1rem 1.5rem;
}
.link-pages .nav.next {
  padding: 0.5rem 3.9rem 0.5rem 2.3rem;
  background: url("/common/img/arr_gray02.png") no-repeat right 1.4rem center #fff;
  background-size: 1rem 1.5rem;
}
@media screen and (max-width: 767px) {
  .link-pages {
    margin-top: 3rem;
    padding: 1.5rem 0;
    gap: 1rem;
  }
  .link-pages > a {
    font-size: 1rem;
    line-height: 1;
    padding: 0.6rem 0.5rem;
    width: 2.4rem;
  }
  .link-pages .nav {
    padding: 0.5rem 3rem;
    border-radius: 2rem;
  }
  .link-pages .nav.prev {
    padding: 0.5rem 1.5rem 0.5rem 2.1rem;
    background-position: 0.8rem center;
    background-size: 0.6rem 0.9rem;
  }
  .link-pages .nav.next {
    padding: 0.5rem 2.1rem 0.5rem 1.5rem;
    background-position: right 0.8rem center;
    background-size: 0.6rem 0.9rem;
  }
}

.modal-wrap {
  display: none;
}

.okk-breadcrumb {
  padding: 2rem 0;
}
.okk-breadcrumb ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 1rem;
}
.okk-breadcrumb li {
  font-size: 1.4rem;
  line-height: 1.4285714286;
  font-weight: 500;
}
.okk-breadcrumb li:not(:last-child) {
  padding-right: 1.4rem;
  position: relative;
}
.okk-breadcrumb li:not(:last-child)::after {
  position: absolute;
  content: "";
  width: 0.65rem;
  height: 1.1rem;
  top: 55%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background: url("../img/common/ico_arrow_breadcrumb.png") no-repeat right center/cover;
  z-index: 1;
}
.okk-breadcrumb a {
  text-decoration: underline;
  text-underline-offset: 2px;
  color: inherit;
}
@media screen and (min-width: 768px) {
  .okk-breadcrumb a:hover {
    text-decoration: none;
  }
}
.okk-breadcrumb + .okk-section-head {
  margin-top: 2rem;
}
@media screen and (max-width: 767px) {
  .okk-breadcrumb {
    padding: 1.3rem 0 2rem;
  }
  .okk-breadcrumb li {
    font-size: 1.2rem;
    line-height: 1.4166666667;
  }
  .okk-breadcrumb li:not(:last-child)::after {
    width: 0.55rem;
    height: 0.975rem;
  }
}

.okk-storeinfo-mv {
  position: relative;
}
.okk-storeinfo-mv__bg {
  overflow: hidden;
}
@media screen and (min-width: 768px) {
  .okk-storeinfo-mv__bg {
    height: 48.9rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-storeinfo-mv__bg {
    aspect-ratio: 375/201;
  }
}
.okk-storeinfo-mv__bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
  -o-object-position: top center;
     object-position: top center;
}
.okk-storeinfo-mv__ttl {
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 1;
  background: #fff;
  font-weight: 700;
  border-radius: 2.5rem 2.5rem 0 0;
  padding: 2rem 3rem 1rem;
  width: 54.3rem;
  text-align: center;
  font-size: 4rem;
  line-height: 1.25;
}
@media screen and (max-width: 767px) {
  .okk-storeinfo-mv__ttl {
    border-radius: 1.5rem 1.5rem 0 0;
    padding: 1.5rem 3rem 0.8rem;
    width: 33.5rem;
    font-size: 2.6rem;
    line-height: 1.4230769231;
  }
}

.okk-mv {
  position: relative;
}
.okk-mv__bg {
  overflow: hidden;
}
@media screen and (min-width: 768px) {
  .okk-mv__bg {
    height: 48.9rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-mv__bg {
    aspect-ratio: 375/201;
  }
}
.okk-mv__bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
  -o-object-position: top center;
     object-position: top center;
}
.okk-mv__ttl {
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 1;
  background: #fff;
  font-weight: 700;
  border-radius: 2.5rem 2.5rem 0 0;
  padding: 2rem 3rem 1rem;
  width: 54.3rem;
  text-align: center;
  font-size: 4rem;
  line-height: 1.25;
}
@media screen and (max-width: 767px) {
  .okk-mv__ttl {
    border-radius: 1.5rem 1.5rem 0 0;
    padding: 1.5rem 3rem 0.8rem;
    width: 33.5rem;
    font-size: 2.6rem;
    line-height: 1.4230769231;
  }
}

.okk-store-name-top {
  border-top: 1px solid #fff;
  background: #d98794;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-weight: 700;
  font-size: 4rem;
  line-height: 1.15;
  height: 12rem;
  position: relative;
}
.okk-store-name-top .txt {
  max-width: 119.6rem;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  .okk-store-name-top {
    font-size: 2.6rem;
    line-height: 1.3461538462;
    height: 7rem;
  }
}

.okk-store-mv {
  margin-top: 1rem;
}
.okk-store-mv__bg {
  border-radius: 2rem;
  overflow: hidden;
}
@media screen and (min-width: 768px) {
  .okk-store-mv__bg {
    height: 34.4rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-store-mv__bg {
    aspect-ratio: 335/180;
  }
}
.okk-store-mv__bg img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
}
.okk-store-mv + .okk-section-head {
  padding-top: 3rem;
  padding-bottom: 1rem;
}
@media screen and (min-width: 768px) {
  .okk-store-mv + .okk-section-head .okk-headline .txt {
    font-size: 3.6rem;
    line-height: 1.4444444444;
  }
}
@media screen and (max-width: 767px) {
  .okk-store-mv + .okk-section-head {
    padding-bottom: 0;
  }
}

.okk-store-map {
  padding-bottom: 8rem;
}
@media screen and (min-width: 768px) {
  .okk-store-map .okk-section-head {
    margin-top: 4.5rem;
  }
}
.okk-store-map-note {
  margin-bottom: 4rem;
}
.okk-store-map-note ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.8rem;
}
.okk-store-map-note li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.8rem;
  line-height: 1.5;
  font-weight: 500;
}
.okk-store-map-note li img {
  width: 2.157rem;
}
.okk-store-map-local {
  position: relative;
}
.okk-store-map-local__iframe {
  width: 100%;
  border: 1px solid #707070;
  border-radius: 2rem;
}
.okk-store-map-local__point {
  --map-width: 1156;
  --map-height: 745;
}
.okk-store-map-local__point a {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition: 0.25s;
  transition: 0.25s;
  z-index: 1;
  opacity: 1;
}
@media screen and (min-width: 768px) {
  .okk-store-map-local__point a:hover {
    opacity: 0.75;
  }
}
.okk-store-map-local__point #map-store-btn-01 {
  width: 11.682rem;
  width: calc(116.82 / var(--map-width) * 100%);
  right: 30.8rem;
  right: calc(308 / var(--map-width) * 100%);
  bottom: 11rem;
  bottom: calc(110 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-02 {
  width: 11.594rem;
  width: calc(115.94 / var(--map-width) * 100%);
  right: 18.4rem;
  right: calc(184 / var(--map-width) * 100%);
  bottom: 15rem;
  bottom: calc(150 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-03 {
  width: 14.793rem;
  width: calc(147.93 / var(--map-width) * 100%);
  right: 53rem;
  right: calc(530 / var(--map-width) * 100%);
  bottom: 25.8rem;
  bottom: calc(258 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-04 {
  width: 12.507rem;
  width: calc(125.07 / var(--map-width) * 100%);
  right: 22.3rem;
  right: calc(223 / var(--map-width) * 100%);
  top: 21rem;
  top: calc(210 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-05 {
  width: 16.2rem;
  width: calc(162 / var(--map-width) * 100%);
  right: 34.5rem;
  right: calc(345 / var(--map-width) * 100%);
  top: 5rem;
  top: calc(50 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-06 {
  width: 13.517rem;
  width: calc(135.17 / var(--map-width) * 100%);
  left: 25.6rem;
  left: calc(256 / var(--map-width) * 100%);
  bottom: 17.8rem;
  bottom: calc(178 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-store-btn-07 {
  width: 12.23rem;
  width: calc(122.3 / var(--map-width) * 100%);
  left: 24.9rem;
  left: calc(249 / var(--map-width) * 100%);
  top: 19.1rem;
  top: calc(191 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-01 {
  width: 16.008rem;
  width: calc(160.08 / var(--map-width) * 100%);
  right: 39.4rem;
  right: calc(394 / var(--map-width) * 100%);
  bottom: 1.4rem;
  bottom: calc(14 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-02 {
  width: 14.178rem;
  width: calc(141.78 / var(--map-width) * 100%);
  right: 41rem;
  right: calc(410 / var(--map-width) * 100%);
  bottom: 4.9rem;
  bottom: calc(49 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-03 {
  width: 14.241rem;
  width: calc(142.41 / var(--map-width) * 100%);
  right: 9.3rem;
  right: calc(93 / var(--map-width) * 100%);
  bottom: 5rem;
  bottom: calc(50 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-04 {
  width: 17.256rem;
  width: calc(172.56 / var(--map-width) * 100%);
  right: 17.2rem;
  right: calc(172 / var(--map-width) * 100%);
  bottom: 23.1rem;
  bottom: calc(231 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-05 {
  width: 18.707rem;
  width: calc(187.07 / var(--map-width) * 100%);
  right: 22.3rem;
  right: calc(223 / var(--map-width) * 100%);
  bottom: 31.8rem;
  bottom: calc(318 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-06 {
  width: 9.787rem;
  width: calc(97.87 / var(--map-width) * 100%);
  right: 23.8rem;
  right: calc(238 / var(--map-width) * 100%);
  bottom: 44.4rem;
  bottom: calc(444 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-07 {
  width: 3.755rem;
  width: calc(37.55 / var(--map-width) * 100%);
  right: 62.8rem;
  right: calc(628 / var(--map-width) * 100%);
  top: 12.5rem;
  top: calc(125 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-08 {
  width: 13.575rem;
  width: calc(135.75 / var(--map-width) * 100%);
  right: 20.9rem;
  right: calc(209 / var(--map-width) * 100%);
  top: 8.6rem;
  top: calc(86 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-09 {
  width: 15.027rem;
  width: calc(150.27 / var(--map-width) * 100%);
  left: 28rem;
  left: calc(280 / var(--map-width) * 100%);
  bottom: 8.2rem;
  bottom: calc(82 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-10 {
  width: 10.171rem;
  width: calc(101.71 / var(--map-width) * 100%);
  left: 27.7rem;
  left: calc(277 / var(--map-width) * 100%);
  bottom: 33rem;
  bottom: calc(330 / var(--map-height) * 100%);
}
.okk-store-map-local__point #map-scenic-btn-11 {
  width: 11.937rem;
  width: calc(119.37 / var(--map-width) * 100%);
  left: 11.6rem;
  left: calc(116 / var(--map-width) * 100%);
  top: 19.5rem;
  top: calc(195 / var(--map-height) * 100%);
}
.okk-section-head + .okk-store-map .okk-section-head:first-child {
  display: none;
}
@media screen and (max-width: 767px) {
  .okk-store-map {
    padding-bottom: 6rem;
  }
  .okk-store-map-note {
    margin-bottom: 2rem;
  }
  .okk-store-map-note ul {
    gap: 0.6rem;
  }
  .okk-store-map-note li {
    gap: 0.6rem;
  }
  .okk-store-map-note li img {
    width: 1.837rem;
  }
  .okk-store-map-local__iframe {
    border-radius: 1.2rem;
  }
}

.okk-map-modal {
  width: 68rem;
  margin: 0 auto;
  padding: 4rem;
  background: #fff;
  border-radius: 2rem;
  position: relative;
}
.okk-map-modal__ttl {
  text-align: center;
  font-size: 3.6rem;
  line-height: 1.4444444444;
  font-weight: 700;
  margin-bottom: 2rem;
}
.okk-map-modal__photo {
  width: 100%;
  overflow: hidden;
  border-radius: 2.5rem;
}
.okk-map-modal__photo a {
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
@media screen and (min-width: 768px) {
  .okk-map-modal__photo a:hover {
    opacity: 0.75;
  }
}
.okk-map-modal__photo img {
  width: 100%;
  display: block;
}
.okk-map-modal__btn {
  margin-top: 3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-map-modal__btn a {
  width: 32rem;
  padding: 1.6rem 4rem;
}
.okk-map-modal__copy {
  margin-top: 1.7rem;
  font-weight: 500;
  line-height: 1.875;
}
.okk-map-modal__slider .slick-arrow {
  width: 6.6rem;
  height: 6.6rem;
  border: 2px solid #413e3e;
  background: #fff;
  border-radius: 50%;
  margin-top: 3.6rem;
  z-index: 2;
}
.okk-map-modal__slider .slick-arrow::before {
  content: none;
}
.okk-map-modal__slider .slick-arrow::after {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  z-index: 1;
  width: 1.5rem;
  height: 1.1727272727rem;
  background: no-repeat center center/100% 100%;
}
.okk-map-modal__slider .slick-arrow.slick-prev {
  left: -12.6rem;
}
.okk-map-modal__slider .slick-arrow.slick-prev::after {
  background-image: url("../img/common/ico_arrow_left.png");
  background-position: center left;
}
.okk-map-modal__slider .slick-arrow.slick-next {
  right: -12.6rem;
}
.okk-map-modal__slider .slick-arrow.slick-next::after {
  background-image: url("../img/common/ico_arrow_right.png");
  background-position: center right;
}
.okk-map-modal .slider-counter {
  position: absolute;
  bottom: -5.2rem;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 2;
  color: #fff;
  font-size: 2rem;
  line-height: 1.6;
  font-weight: 500;
}
.okk-map-modal .mfp-close {
  color: transparent;
  text-indent: -99999px;
  opacity: 1;
  top: -2.5rem;
  right: -10rem;
  width: 3.4rem;
  height: 2.5rem;
  background: url("../img/common/map/ico_close_map.svg") no-repeat center center/cover;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
@media screen and (min-width: 768px) {
  .okk-map-modal .mfp-close:hover {
    opacity: 0.75;
  }
}
@media screen and (max-width: 767px) {
  .okk-map-modal {
    width: 50rem;
    padding: 2rem;
    border-radius: 1.8rem;
  }
  .okk-map-modal__ttl {
    font-size: 2.6rem;
    line-height: 1.3846153846;
  }
  .okk-map-modal__photo {
    border-radius: 1.2rem;
  }
  .okk-map-modal__btn {
    margin-top: 2rem;
  }
  .okk-map-modal__btn a {
    width: 25rem;
    padding: 1.1rem 4rem;
  }
  .okk-map-modal__copy {
    margin-top: 1.2rem;
  }
  .okk-map-modal__slider .slick-arrow {
    width: 4rem;
    height: 4rem;
    border-width: 1px;
    margin-top: 0;
    top: auto;
    bottom: -8rem;
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
  .okk-map-modal__slider .slick-arrow::after {
    width: 1.2rem;
    height: 0.9381818182rem;
  }
  .okk-map-modal__slider .slick-arrow.slick-prev {
    left: 6rem;
  }
  .okk-map-modal__slider .slick-arrow.slick-next {
    right: 6rem;
  }
  .okk-map-modal .slider-counter {
    bottom: -5.1rem;
    font-size: 1.6rem;
    line-height: 1.5;
  }
  .okk-map-modal .mfp-close {
    top: -4rem;
    right: 0;
  }
}
@media screen and (max-width: 540px) {
  .okk-map-modal {
    width: calc(100vw - 4rem);
  }
  .okk-map-modal__copy {
    margin-top: 1rem;
    font-size: 1.2rem;
  }
}

.okk-store {
  padding-top: 4rem;
}
.okk-store__unit {
  padding: 0 0 10rem;
}
.okk-store__slider {
  overflow: hidden;
}
.okk-store__slider .slide-item {
  padding: 0 1rem;
  max-width: 62rem;
  height: 43rem;
}
.okk-store__slider .slide-item img {
  border-radius: 2.5rem;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  max-width: 100%;
  height: 100%;
}
.okk-store__slider .slick-arrow {
  width: 6.6rem;
  height: 6.6rem;
  border: 2px solid #413e3e;
  background: #fff;
  border-radius: 50%;
  z-index: 2;
}
.okk-store__slider .slick-arrow::before {
  content: none;
}
.okk-store__slider .slick-arrow::after {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  z-index: 1;
  width: 1.5rem;
  height: 1.1727272727rem;
  background: no-repeat center center/100% 100%;
}
.okk-store__slider .slick-arrow.slick-prev {
  left: 0;
  left: calc(50% - 31rem - 6.6rem);
}
.okk-store__slider .slick-arrow.slick-prev::after {
  background-image: url("../img/common/ico_arrow_left.png");
  background-position: center left;
}
.okk-store__slider .slick-arrow.slick-next {
  right: 0;
  right: calc(50% - 31rem - 6.6rem);
}
.okk-store__slider .slick-arrow.slick-next::after {
  background-image: url("../img/common/ico_arrow_right.png");
  background-position: center right;
}
.okk-store__desc {
  padding-top: 4rem;
}
.okk-store__ttl {
  text-align: center;
  display: block;
  font-size: 3.2rem;
  line-height: 1.4375;
  font-weight: 700;
  letter-spacing: 0;
}
.okk-store__copy {
  text-align: center;
  line-height: 1.875;
  font-weight: 500;
  letter-spacing: 0;
  margin-top: 2rem;
}
.okk-store__btn {
  margin-top: 4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 2rem;
}
.okk-store__btn .okk-btn {
  width: 32rem;
}
@media screen and (max-width: 767px) {
  .okk-store {
    padding-top: 0;
  }
  .okk-store__unit {
    padding: 0 0 6rem;
  }
  .okk-store__slider .slide-item {
    max-width: 40.2rem;
    height: 27.4rem;
  }
  .okk-store__slider .slide-item img {
    border-radius: 1.2rem;
  }
  .okk-store__slider .slick-arrow {
    width: 4rem;
    height: 4rem;
    border-width: 1px;
  }
  .okk-store__slider .slick-arrow::after {
    width: 1.2rem;
    height: 0.9381818182rem;
  }
  .okk-store__slider .slick-arrow.slick-prev {
    left: calc(50% - 20.1rem - 4rem);
  }
  .okk-store__slider .slick-arrow.slick-next {
    right: calc(50% - 20.1rem - 4rem);
  }
  .okk-store__desc {
    padding-top: 2rem;
  }
  .okk-store__ttl {
    font-size: 2.6rem;
    line-height: 1.3846153846;
  }
  .okk-store__btn {
    margin-top: 2rem;
    gap: 1rem;
  }
  .okk-store__btn li {
    padding: 0 0.5rem;
  }
  .okk-store__btn .okk-btn {
    width: 25rem;
    padding: 1.1rem 4rem;
  }
}
@media screen and (max-width: 540px) {
  .okk-store__slider .slide-item {
    max-width: 25.7rem;
    height: 17rem;
  }
  .okk-store__slider .slick-arrow.slick-prev {
    left: calc(50% - 12.85rem - 4rem);
  }
  .okk-store__slider .slick-arrow.slick-next {
    right: calc(50% - 12.85rem - 4rem);
  }
  .okk-store__copy {
    text-align: left;
  }
  .okk-store__btn li {
    padding: 0;
  }
}

.okk-store-access {
  padding-bottom: 10rem;
}
.okk-store-access__img {
  border: 1px solid #707070;
  border-radius: 2rem;
  overflow: hidden;
}
.okk-store-access__img img {
  display: block;
}
@media screen and (max-width: 767px) {
  .okk-store-access {
    padding-bottom: 6rem;
  }
  .okk-store-access .okk-section-head {
    margin-bottom: 2rem;
  }
  .okk-store-access__img {
    border-radius: 1.8rem;
  }
}

.okk-store-detail {
  padding: 7rem 0 8rem;
}
.okk-store-detail__box {
  border-radius: 2rem;
  background: #f8f8f8;
  padding: 4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 4rem;
}
.okk-store-detail__pic {
  width: 51.9rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  border-radius: 1.2rem;
  overflow: hidden;
}
.okk-store-detail__desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  line-height: 1.875;
  font-weight: 500;
}
.okk-store-detail__ttl {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  color: #d98794;
  margin-bottom: 2rem;
}
.okk-store-detail__copy dl {
  margin-top: 0.2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 0 1em;
}
.okk-store-detail__copy dl dt {
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  .okk-store-detail {
    padding: 4rem 0 5rem;
  }
  .okk-store-detail__box {
    border-radius: 1.2rem;
    padding: 2rem 2rem 3rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 2rem;
  }
  .okk-store-detail__pic {
    width: 100%;
    border-radius: 1rem;
  }
  .okk-store-detail__ttl {
    font-size: 1.8rem;
    line-height: 1.4444444444;
    margin-bottom: 1.4rem;
  }
  .okk-store-detail__copy dl {
    margin-top: 0.2rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 0 1em;
  }
  .okk-store-detail__copy dl dt {
    font-weight: 700;
  }
}

.okk-store-anchor {
  margin-top: 4.4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.okk-store-anchor a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 1.6rem;
  line-height: 1.5;
  padding: 2.2rem 3rem;
  border: 2px solid #413e3e;
  font-weight: 700;
  border-radius: 7rem;
  text-align: center;
}
.okk-store-anchor a span {
  padding: 0 1.6rem;
  position: relative;
}
.okk-store-anchor a span::after {
  position: absolute;
  content: "";
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
  height: 1.208rem;
  width: 0.843rem;
  background: url("../img/common/ico_down.png") no-repeat bottom center/cover;
}
@media screen and (max-width: 767px) {
  .okk-store-anchor {
    margin-top: 3rem;
  }
  .okk-store-anchor a {
    width: 100%;
    max-width: 33.5rem;
    padding: 1.2rem 4.7rem;
    position: relative;
  }
  .okk-store-anchor a span {
    position: static;
  }
  .okk-store-anchor a span::after {
    right: 2.8rem;
    height: 1.5rem;
    width: 1.0467715232rem;
  }
}

.okk-store-video {
  margin: 6rem auto 0;
  max-width: 71.8rem;
}
@media screen and (max-width: 767px) {
  .okk-store-video {
    margin-top: 5rem;
  }
}

.okk-store-google-map__iframe {
  overflow: hidden;
  aspect-ratio: 1156/592;
}
@media screen and (max-width: 767px) {
  .okk-store-google-map__iframe {
    aspect-ratio: 335/268;
  }
}
.okk-store-google-map__iframe iframe {
  width: 100%;
  height: 100%;
}
.okk-store-google-map__iframe a {
  width: 100%;
  height: 100%;
  display: block;
}
.okk-store-google-map__iframe a img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.okk-store-google-map-search {
  margin-top: 8rem;
  text-align: center;
}
.okk-store-google-map-search__box {
  margin-top: 4rem;
  background: #fff;
  border-radius: 2rem;
  padding: 4rem;
}
.okk-store-google-map-search__box .box-lead {
  line-height: 1.875;
  font-weight: 500;
}
.okk-store-google-map-search__box .box-copy {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid #dedede;
  overflow: hidden;
  border-radius: 1.2rem;
  background: #fff;
  width: 51.8rem;
  margin: 3rem auto 0;
}
.okk-store-google-map-search__box .box-copy__text {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  text-align: center;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.okk-store-google-map-search__box .box-copy__btn {
  width: 8.2rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  background: #f8f8f8;
  border: none;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  height: 8rem;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-store-google-map-search__box .box-copy__btn img {
  width: 2.4rem;
  height: 2.85rem;
}
.okk-store-google-map-search__box .box-copy__btn .copy-status {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 1;
  display: none;
  width: 4rem;
  height: 4rem;
  background: #f8f8f8;
}
.okk-store-google-map-search__box .box-copy__btn .copy-status img {
  width: 100%;
  height: 100%;
}
@media screen and (min-width: 768px) {
  .okk-store-google-map-search__box .box-copy__btn:hover {
    opacity: 0.75;
  }
}
@media screen and (max-width: 767px) {
  .okk-store-google-map-search {
    margin-top: 5rem;
  }
  .okk-store-google-map-search__box {
    margin-top: 3rem;
    border-radius: 1.2rem;
    padding: 2.2rem 2rem;
  }
  .okk-store-google-map-search__box .box-lead {
    text-align: left;
  }
  .okk-store-google-map-search__box .box-copy {
    border-radius: 1rem;
    margin: 2rem auto 0;
    width: 100%;
    max-width: 30rem;
  }
  .okk-store-google-map-search__box .box-copy__text {
    font-size: 1.4rem;
    line-height: 1.4285714286;
  }
  .okk-store-google-map-search__box .box-copy__btn {
    width: 4.5rem;
    height: 4.5rem;
  }
  .okk-store-google-map-search__box .box-copy__btn img {
    width: 1.38rem;
    height: 1.639rem;
  }
  .okk-store-google-map-search__box .box-copy__btn .copy-status {
    width: 2.2rem;
    height: 2.2rem;
  }
}

.okk-store-map-access {
  padding: 6rem 0 8rem;
}
.okk-store-map-access .okk-headline {
  text-align: center;
  margin-bottom: 3rem;
}
.okk-store-map-access__content {
  background: #f8f8f8;
  padding: 4rem;
  border-radius: 2rem;
}
.okk-store-map-access__ttl {
  font-size: 3.2rem;
  line-height: 1.4375;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3.5rem;
}
.okk-store-map-access__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 1.4rem 4rem;
}
.okk-store-map-access__list dl {
  width: calc(50% - 2rem);
  border-radius: 1.2rem;
  background: #fff;
  padding: 2rem;
}
.okk-store-map-access__list dl dt {
  font-size: 2.4rem;
  line-height: 1.4583333333;
  font-weight: 700;
  color: #d98794;
  margin-bottom: 1rem;
}
.okk-store-map-access__list dl dd {
  font-weight: 500;
  line-height: 1.75;
}
.okk-store-map-access__list dl dd li + li {
  margin-top: 0.2rem;
}
.okk-store-map-access__note {
  margin-top: 2rem;
}
.okk-store-map-access__note li {
  line-height: 1.875;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  .okk-store-map-access {
    padding: 5rem 0 6rem;
  }
  .okk-store-map-access .okk-headline {
    margin-bottom: 2rem;
  }
  .okk-store-map-access__content {
    padding: 2.2rem 2rem;
    border-radius: 1.2rem;
  }
  .okk-store-map-access__ttl {
    font-size: 1.8rem;
    line-height: 1.5555555556;
    margin-bottom: 1.4rem;
  }
  .okk-store-map-access__list {
    gap: 0.8rem;
  }
  .okk-store-map-access__list dl {
    width: 100%;
    border-radius: 1rem;
    padding: 1.2rem 2rem;
  }
  .okk-store-map-access__list dl dt {
    font-size: 1.6rem;
    line-height: 1.875;
    margin-bottom: 0.6rem;
  }
  .okk-store-map-access__list dl dd {
    font-size: 1.2rem;
  }
  .okk-store-map-access__list dl dd li + li {
    margin-top: 0.1rem;
  }
  .okk-store-map-access__note {
    margin-top: 1.2rem;
  }
  .okk-store-map-access__note li {
    font-size: 1.2rem;
  }
}

.okk-cm-service .okk-section-head + .okk-ls-services {
  margin-top: 0;
}
.okk-cm-service.okk-bg-gray .okk-ls-services {
  background: #fff;
}
.okk-cm-service.okk-bg-gray .okk-ls-services__item__inner {
  background: #f8f8f8;
}

.okk-anchor {
  padding: 3rem 0 6rem;
}
.okk-anchor__box {
  background: #f8f8f8;
  padding: 3rem 4rem 4rem;
  border-radius: 1.2rem;
}
.okk-anchor__list {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: minmax(0, 1fr) 2rem minmax(0, 1fr) 2rem minmax(0, 1fr);
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 1.4rem 2rem;
}
.okk-anchor__btn {
  height: 100%;
  background: #fff;
  border-radius: 1.2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #d98794;
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
  padding: 1.6rem 3rem 1.6rem 2rem;
  position: relative;
}
.okk-anchor__btn::after {
  position: absolute;
  content: "";
  top: 50%;
  right: 1.6rem;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1.1rem;
  height: 1.4rem;
  z-index: 1;
  background: url("../img/common/ico_down_red.png") no-repeat bottom center/cover;
}
@media screen and (max-width: 767px) {
  .okk-anchor {
    padding: 2rem 0 4rem;
  }
  .okk-anchor__box {
    padding: 2rem 2rem 2.2rem;
  }
  .okk-anchor__list {
    -ms-grid-columns: minmax(0, 1fr) 0.9rem minmax(0, 1fr);
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem 0.9rem;
  }
  .okk-anchor__btn {
    border-radius: 1rem;
    font-size: 1.2rem;
    line-height: 1.5;
    padding: 1rem 1.6rem 1rem 1rem;
  }
  .okk-anchor__btn::after {
    right: 0.8rem;
    width: 0.8rem;
    height: 1rem;
  }
}
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .okk-anchor__list {
    -ms-grid-columns: (minmax(0, 1fr))[2];
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.okk-anchor02__list {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: minmax(0, 1fr) 2.6rem minmax(0, 1fr) 2.6rem minmax(0, 1fr) 2.6rem minmax(0, 1fr);
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 1.5rem 2.6rem;
}
.okk-anchor02__btn {
  height: 100%;
  background: #fff;
  border-radius: 1.2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.3rem;
  color: inherit;
  font-size: 2rem;
  line-height: 1.45;
  font-weight: 700;
  padding: 1.2rem 2rem 2.2rem;
  position: relative;
}
.okk-anchor02__btn::after {
  position: absolute;
  content: "";
  bottom: 1rem;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 1.3rem;
  height: 0.75rem;
  z-index: 1;
  background: url("../img/common/ico_arrow_returnplan.png") no-repeat bottom center/cover;
}
.okk-anchor02__btn .ico {
  width: 4.4rem;
}
@media screen and (max-width: 767px) {
  .okk-anchor02__list {
    -ms-grid-columns: minmax(0, 1fr) 1.5rem minmax(0, 1fr);
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }
  .okk-anchor02__btn {
    border-radius: 1rem;
    gap: 0.4rem;
    font-size: 1.6rem;
    line-height: 1.5;
    padding: 1rem 2rem 2rem;
  }
  .okk-anchor02__btn .ico {
    width: 3.6rem;
  }
}
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .okk-anchor02__list {
    -ms-grid-columns: (minmax(0, 1fr))[3];
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.okk-faq__item {
  background: #fff;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 1.2rem;
}
.okk-faq__item:nth-child(n+2) {
  margin-top: 1.6rem;
}
.okk-faq__question {
  cursor: pointer;
  padding: 2.4rem 8.2rem 2.4rem 4rem;
  font-weight: 700;
  font-size: 1.8rem;
  line-height: 1.4444444444;
  color: #d98794;
  position: relative;
}
.okk-faq__question::before {
  position: absolute;
  content: "";
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 3rem;
  width: 3.2rem;
  height: 3.2rem;
  z-index: 1;
  border: 1px solid #413e3e;
  border-radius: 50%;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-faq__question .txt::before, .okk-faq__question .txt::after {
  position: absolute;
  content: "";
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 4.1rem;
  width: 1rem;
  height: 1px;
  background: #413e3e;
  border-radius: 1rem;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-faq__question .txt::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
.okk-faq__question.is-active .txt::after {
  opacity: 0 !important;
}
@media (hover: hover) {
  .okk-faq__question:hover::before,
  .okk-faq__question:hover .txt::before,
  .okk-faq__question:hover .txt::after {
    opacity: 0.75;
  }
}
.okk-faq__answer {
  border-top: 1px solid #c3c3c3;
  padding: 2.2rem 4rem;
  font-size: 1.6rem;
  line-height: 2;
  font-weight: 500;
}
.okk-faq__answer .txt a {
  text-decoration: underline;
  text-underline-offset: 2px;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
@media screen and (min-width: 768px) {
  .okk-faq__answer .txt a:hover {
    opacity: 0.75;
    text-decoration: none;
  }
}
.okk-faq__answer .faq-txt-btn {
  padding: 1.2rem 2rem;
  text-decoration: none !important;
  margin-top: 1rem;
}
@media screen and (min-width: 768px) {
  .okk-faq__answer .faq-txt-btn {
    max-width: 25rem;
  }
}
@media screen and (max-width: 767px) {
  .okk-faq__item:nth-child(n+2) {
    margin-top: 1rem;
  }
  .okk-faq__question {
    padding: 1.4rem 6.6rem 1.4rem 2rem;
    font-size: 1.6rem;
    line-height: 1.625;
    min-height: 6rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .okk-faq__question::before {
    right: 1.4rem;
  }
  .okk-faq__question .txt::before, .okk-faq__question .txt::after {
    right: 2.5rem;
  }
  .okk-faq__answer {
    padding: 2rem;
    font-size: 1.4rem;
    line-height: 1.8571428571;
  }
}
.okk-home .okk-faq__question {
  cursor: pointer;
  padding: 1.8rem 7rem 1.8rem 2rem;
  font-size: 1.6rem;
  line-height: 1.75;
}
.okk-home .okk-faq__question::before {
  right: 2rem;
}
.okk-home .okk-faq__question .txt::before, .okk-home .okk-faq__question .txt::after {
  right: 3.1rem;
}
.okk-home .okk-faq__answer {
  border-top-color: #e0e0e0;
  padding: 2rem;
  font-size: 1.4rem;
  line-height: 1.7142857143;
}
@media (min-width: 768px) {
  .okk-home .okk-faq__items {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .okk-home .okk-faq__item:nth-child(n+2) {
    margin-top: 2rem;
  }
  .okk-home .okk-faq__items_col {
    width: calc(50% - 1rem);
  }
}
@media (max-width: 767px) {
  .okk-home .okk-faq__items_col:nth-child(n+2) {
    margin-top: 1rem;
  }
  .okk-home .okk-faq__question {
    min-height: unset;
  }
}

.okk-about-point {
  margin-top: 4rem;
  background: #faf2f4;
  padding: 4rem 0 5rem;
}
.okk-about-point__list {
  display: -ms-grid;
  display: grid;
  gap: 4rem 3.8rem;
  -ms-grid-columns: minmax(0, 1fr) 3.8rem minmax(0, 1fr) 3.8rem minmax(0, 1fr);
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.okk-about-point__item {
  height: 100%;
  background: #fff;
  border-radius: 2.5rem 2.5rem 1.2rem 1.2rem;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.okk-about-point__img {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  aspect-ratio: 360/206;
}
.okk-about-point__desc {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 1.7rem 1rem 2.5rem;
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media screen and (max-width: 767px) {
  .okk-about-point {
    margin-top: 1rem;
    padding: 3rem 0 4rem;
  }
  .okk-about-point__list {
    gap: 1rem 1.1rem;
    -ms-grid-columns: minmax(0, 1fr) 1.1rem minmax(0, 1fr);
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .okk-about-point__item {
    border-radius: 1.2rem;
  }
  .okk-about-point__img {
    aspect-ratio: 162/93;
  }
  .okk-about-point__desc {
    padding: 1.2rem 1rem;
    font-size: 1.4rem;
    line-height: 1.4285714286;
  }
}
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .okk-about-point__list {
    -ms-grid-columns: (minmax(0, 1fr))[2];
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
.okk-about-history__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2rem;
}
.okk-about-history__item:nth-child(n+2) {
  margin-top: 4rem;
}
.okk-about-history .figure {
  border-radius: 1.2rem;
  aspect-ratio: 335/200;
}
.okk-about-history .figure--sp {
  margin-top: 4rem;
}
.okk-about-history__ttl {
  color: #90b5d1;
  font-weight: 700;
  font-size: 1.8rem;
  line-height: 1.5555555556;
  margin-bottom: 2rem;
}
.okk-about-history__copy p:nth-of-type(n + 2) {
  margin-top: 2rem;
}
@media screen and (min-width: 768px) {
  .okk-about-history__item {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    gap: 4rem;
  }
  .okk-about-history__item:nth-child(n+2) {
    margin-top: 4rem;
  }
  .okk-about-history__item:nth-child(even) {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .okk-about-history__img {
    width: 55.8rem;
    max-width: calc(50% - 2rem);
    -ms-flex-negative: 0;
        flex-shrink: 0;
  }
  .okk-about-history .figure {
    border-radius: 2.5rem;
    aspect-ratio: 558/400;
  }
  .okk-about-history .figure:nth-of-type(n + 2) {
    margin-top: 2rem;
  }
  .okk-about-history__desc {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
  .okk-about-history__ttl {
    font-size: 2.4rem;
    line-height: 1.5833333333;
    margin-bottom: 4rem;
  }
  .okk-about-history__copy p:nth-of-type(n + 2) {
    margin-top: 4rem;
  }
}
.okk-about-feature__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 3rem;
}
.okk-about-feature__item {
  background: #fff;
  padding: 2rem 2rem 3rem;
  border-radius: 1.2rem;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2rem;
}
.okk-about-feature__img {
  aspect-ratio: 295/200;
  border-radius: 1rem;
}
.okk-about-feature__ttl {
  color: #d98794;
  font-size: 1.8rem;
  line-height: 1.5555555556;
  font-weight: 700;
  margin-bottom: 2rem;
}
@media screen and (min-width: 768px) {
  .okk-about-feature__list {
    gap: 4rem;
  }
  .okk-about-feature__item {
    padding: 4rem;
    border-radius: 2rem;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    gap: 4rem;
  }
  .okk-about-feature__item:nth-child(even) {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
  }
  .okk-about-feature__img {
    aspect-ratio: 538/451;
    border-radius: 2.5rem;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    width: 50%;
  }
  .okk-about-feature__desc {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
  .okk-about-feature__ttl {
    font-size: 2.4rem;
    line-height: 1.5833333333;
    margin-bottom: 3rem;
  }
}
.okk-about-thoughts__inner {
  position: relative;
}
.okk-about-thoughts__content {
  padding: 6rem 28rem;
  position: relative;
  z-index: 2;
}
.okk-about-thoughts__content .okk-copy {
  text-align: center;
}
.okk-about-thoughts__img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  list-style: none;
}
.okk-about-thoughts__img li {
  width: 26.4rem;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.okk-about-thoughts__img li span {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-repeat: repeat-y;
  background-size: 100% 125.4rem;
  z-index: 1;
}
.okk-about-thoughts__img li:nth-child(1) span {
  -webkit-animation: 25s linear infinite scrollAboutDown;
          animation: 25s linear infinite scrollAboutDown;
  background-image: url("../img/about/thoughts_img01.png");
}
.okk-about-thoughts__img li:nth-child(2) span {
  -webkit-animation: 25s linear infinite scrollAboutUp;
          animation: 25s linear infinite scrollAboutUp;
  background-image: url("../img/about/thoughts_img02.png");
}
@media screen and (max-width: 767px) {
  .okk-about-thoughts {
    padding: 6rem 0;
  }
  .okk-about-thoughts__content {
    padding: 16.3rem 0;
  }
  .okk-about-thoughts__content .okk-section-head {
    margin-bottom: 2rem;
  }
  .okk-about-thoughts__content .okk-copy {
    text-align: left;
  }
  .okk-about-thoughts__img {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin: 0 -2rem;
    width: calc(100% + 4rem);
  }
  .okk-about-thoughts__img li {
    width: 100%;
    height: 13.3rem;
  }
  .okk-about-thoughts__img li span {
    background-repeat: repeat-x;
    background-size: 45.9rem 100%;
  }
  .okk-about-thoughts__img li:nth-child(1) span {
    -webkit-animation: 25s linear infinite scrollAboutRight;
            animation: 25s linear infinite scrollAboutRight;
    background-image: url("../img/about/thoughts_img01_sp.png");
  }
  .okk-about-thoughts__img li:nth-child(2) span {
    -webkit-animation: 25s linear infinite scrollAboutLeft;
            animation: 25s linear infinite scrollAboutLeft;
    background-image: url("../img/about/thoughts_img02_sp.png");
  }
}
.okk-about-greeting {
  padding-bottom: 1rem;
}
.okk-about-greeting__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2rem;
}
.okk-about-greeting__item {
  background: #faf2f4;
  padding: 2rem 2rem 3rem;
  border-radius: 1.2rem;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2rem;
}
.okk-about-greeting__img {
  aspect-ratio: 295/200;
  border-radius: 1rem;
}
.okk-about-greeting__name {
  margin-top: 1.6rem;
  color: #d98794;
  font-size: 1.8rem;
  line-height: 1.5555555556;
  font-weight: 700;
}
@media screen and (min-width: 768px) {
  .okk-about-greeting {
    padding-bottom: 3rem;
  }
  .okk-about-greeting__list {
    gap: 3rem;
  }
  .okk-about-greeting__item {
    padding: 4rem;
    border-radius: 2rem;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    gap: 4rem;
  }
  .okk-about-greeting__img {
    aspect-ratio: 518/400;
    border-radius: 2.5rem;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    width: 50%;
  }
  .okk-about-greeting__desc {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
  }
  .okk-about-greeting__name {
    margin-top: 2rem;
    font-size: 2rem;
    line-height: 1.4;
  }
}

.okk-flow {
  padding: 2.5rem 0 6rem;
  background: #f2f5f7;
}
.okk-flow__items {
  padding-top: 3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 5rem;
}
.okk-flow__item {
  position: relative;
}
.okk-flow__num {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 2;
  border: 2px solid #d98794;
  border-radius: 50%;
  width: 6rem;
  height: 6rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #fff;
  font-size: 2rem;
  line-height: 1.5;
  font-weight: 700;
  color: #d98794;
}
.okk-flow__content {
  border-radius: 1.2rem;
  background: #fff;
  padding: 4.4rem 2rem 3rem;
}
@media screen and (max-width: 767px) {
  .okk-flow__content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.okk-flow__ttl {
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
  color: #d98794;
  margin-bottom: 1rem;
}
@media screen and (max-width: 767px) {
  .okk-flow__ttl {
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1;
    text-align: center;
  }
}
.okk-flow__img {
  margin-bottom: 1.6rem;
}
.okk-flow__img figure {
  overflow: hidden;
  border-radius: 1rem;
}
@media screen and (max-width: 767px) {
  .okk-flow__img figure {
    aspect-ratio: 295/200;
  }
  .okk-flow__img figure img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
.okk-flow__desc dt {
  margin-bottom: 0.8rem;
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  .okk-flow__desc dl + .okk-flow__img {
    margin-top: 3rem;
  }
}
.okk-flow__btn {
  margin-top: 2rem;
}
.okk-flow__btn a + a {
  margin-top: 1rem;
}
@media screen and (min-width: 768px) {
  .okk-flow {
    padding: 4rem 0 8rem;
  }
  .okk-flow__items {
    padding-top: 0;
    gap: 3rem;
    overflow: hidden;
  }
  .okk-flow__item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    gap: 4rem;
  }
  .okk-flow__item:not(:last-child) {
    position: relative;
  }
  .okk-flow__item:not(:last-child)::before {
    content: "";
    position: absolute;
    top: 6rem;
    left: 5.7rem;
    width: 3px;
    height: 100%;
    background: #d98794;
    z-index: 1;
  }
  .okk-flow__num {
    position: relative;
    top: 0;
    left: 0;
    -ms-flex-negative: 0;
        flex-shrink: 0;
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
    border-width: 3px;
    width: 11.7rem;
    height: 11.7rem;
    font-size: 3.8rem;
    line-height: 1.5;
  }
  .okk-flow__content {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    border-radius: 2rem;
    padding: 4rem;
    overflow: hidden;
  }
  .okk-flow__ttl {
    margin-top: 2rem;
    font-size: 2.4rem;
    line-height: 1.4583333333;
    margin-bottom: 2.6rem;
  }
  .okk-flow__img {
    float: left;
    margin-right: 4rem;
    margin-bottom: 0;
    width: 38rem;
  }
  .okk-flow__img figure {
    border-radius: 1.2rem;
  }
  .okk-flow__img figure:nth-child(n+2) {
    margin-top: 2rem;
  }
  .okk-flow__desc {
    padding-left: 42rem;
  }
  .okk-flow__desc dl:nth-of-type(n + 2) {
    margin-top: 3rem;
  }
  .okk-flow__desc dt {
    font-size: 2rem;
    line-height: 1.5;
  }
  .okk-flow__btn {
    margin-top: 3rem;
  }
  .okk-flow__btn a + a {
    margin-top: 1.4rem;
  }
}
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .okk-flow__item:not(:last-child)::before {
    top: 6rem;
    left: 4.5rem;
  }
  .okk-flow__num {
    width: 9.3rem;
    height: 9.3rem;
    font-size: 3.2rem;
  }
  .okk-flow__content {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    border-radius: 2rem;
    padding: 4rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .okk-flow__ttl {
    margin-top: 0;
    margin-bottom: 2rem;
    -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
            order: -1;
  }
  .okk-flow__img {
    float: none;
    margin-right: 0;
    margin-bottom: 2rem;
    width: 38rem;
  }
  .okk-flow__desc {
    padding-left: 0;
  }
  .okk-flow__btn {
    margin-top: 2rem;
  }
}

.okk-returnplan {
  padding: 4rem 0 6rem;
  background: #f2f5f7;
}
.okk-returnplan__items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 3rem;
}
.okk-returnplan__item {
  border-radius: 1.2rem;
  background: #fff;
  padding: 2rem 2rem 3rem;
}
.okk-returnplan__heading-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.6rem;
}
.okk-returnplan__heading-row .okk-heading-lv3 {
  margin-bottom: 0;
}
.okk-returnplan__img {
  margin-bottom: 1.6rem;
}
.okk-returnplan__img figure {
  overflow: hidden;
  border-radius: 1rem;
  position: relative;
}
.okk-returnplan__img figure figcaption {
  position: absolute;
  bottom: 1rem;
  left: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  padding: 0.6rem 1.4rem;
  background: #fff;
  font-size: 1.2rem;
  line-height: 1.4166666667;
  font-weight: 500;
  border-radius: 4rem;
  text-align: center;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 90%;
}
.okk-returnplan__desc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 1.6rem;
}
@media screen and (max-width: 767px) {
  .okk-returnplan__desc .okk-list-dot {
    font-size: 1.2rem;
  }
  .okk-returnplan__desc .okk-list-dot li {
    padding-left: 1.6rem;
  }
}
.okk-returnplan__desc .okk-note {
  margin-top: 1.6rem;
}
@media screen and (min-width: 1023px) {
  .okk-returnplan {
    padding: 6rem 0 8rem;
  }
  .okk-returnplan__item {
    border-radius: 2rem;
    padding: 4rem;
  }
  .okk-returnplan__heading-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    gap: 3.5rem;
    margin-bottom: 3rem;
  }
  .okk-returnplan__row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    gap: 4rem;
  }
  .okk-returnplan__img {
    margin-bottom: 0;
    width: 53.8rem;
    -ms-flex-negative: 0;
        flex-shrink: 0;
  }
  .okk-returnplan__img figure {
    border-radius: 1.2rem;
  }
  .okk-returnplan__img figure figcaption {
    bottom: 2rem;
    padding: 0.7rem 1.4rem;
    font-size: 1.6rem;
    line-height: 1.5;
    border-radius: 8rem;
  }
  .okk-returnplan__desc {
    -webkit-box-flex: 1;
        -ms-flex-positive: 1;
            flex-grow: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
            flex-direction: column-reverse;
    gap: 2rem;
  }
}

.okk-luggage-storage {
  padding: 4rem 0 6rem;
}
.okk-luggage-storage__box {
  border-radius: 1.2rem;
  background: #f8f8f8;
  padding: 2rem 2rem 3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 2rem;
}
@media screen and (max-width: 767px) {
  .okk-luggage-storage__content .okk-list-dot {
    font-size: 1.2rem;
  }
  .okk-luggage-storage__content .okk-list-dot li {
    padding-left: 1.6rem;
  }
}
.okk-luggage-storage__content-item:nth-child(n+2) {
  margin-top: 2.4rem;
}
@media screen and (min-width: 1023px) {
  .okk-luggage-storage {
    padding: 6rem 0 8rem;
  }
  .okk-luggage-storage__box {
    border-radius: 2rem;
    background: #f8f8f8;
    padding: 4rem 4rem 5rem;
    gap: 3rem;
  }
  .okk-luggage-storage__il {
    max-width: 84.6rem;
    margin: 0 auto;
  }
  .okk-luggage-storage__content-item:nth-child(n+2) {
    margin-top: 3rem;
  }
}

.okk-course {
  padding-bottom: 3rem;
}
.okk-course__content {
  max-width: 94.5rem;
  margin: 0 auto;
}
.okk-course__item {
  background: #fff;
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 1.2rem;
}
.okk-course__item:nth-child(n+2) {
  margin-top: 1.4rem;
}
.okk-course__heading {
  cursor: pointer;
  padding: 1.5rem 8.2rem 1.5rem 4rem;
  font-weight: 700;
  font-size: 2rem;
  line-height: 1.5;
  color: #d98794;
  position: relative;
}
.okk-course__heading::before {
  position: absolute;
  content: "";
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 3rem;
  width: 3.2rem;
  height: 3.2rem;
  z-index: 1;
  border: 1px solid #413e3e;
  border-radius: 50%;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-course__heading .txt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 2.8rem;
}
.okk-course__heading .txt::before, .okk-course__heading .txt::after {
  position: absolute;
  content: "";
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 4.1rem;
  width: 1rem;
  height: 1px;
  background: #413e3e;
  border-radius: 1rem;
  -webkit-transition: 0.25s;
  transition: 0.25s;
}
.okk-course__heading .txt::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
.okk-course__heading .txt .case {
  color: #413e3e;
  font-size: 1.6rem;
  line-height: 1.875;
}
.okk-course__heading.is-active .txt::after {
  opacity: 0 !important;
}
@media (hover: hover) {
  .okk-course__heading:hover::before,
  .okk-course__heading:hover .txt::before,
  .okk-course__heading:hover .txt::after {
    opacity: 0.75;
  }
}
.okk-course__body {
  padding: 2rem 4rem 2.6rem 2.6rem;
}
.okk-course .case-schedule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.okk-course .case-schedule:not(:first-child) {
  margin-top: 3rem;
}
.okk-course .case-schedule__time {
  width: 8.2rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  font-size: 1.6rem;
  line-height: 1.625;
  font-weight: 700;
  padding-right: 0.5rem;
}
.okk-course .case-schedule__content {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.okk-course .case-schedule__content ul + p,
.okk-course .case-schedule__content p + ul {
  margin-top: 1em;
}
.okk-course .case-schedule__title {
  margin-bottom: 2rem;
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
}
.okk-course .case-schedule__image-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: start;
  gap: 1.8rem;
}
.okk-course .case-schedule__image-text + .case-schedule__copy {
  margin-top: 1em;
}
.okk-course .case-schedule__image {
  width: 29.2rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  border-radius: 3rem;
  overflow: hidden;
}
.okk-course .case-schedule__text {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}
.okk-course .case-schedule__copy {
  line-height: 1.625;
}
.okk-course .case-schedule__list-dot li {
  position: relative;
  padding-left: 1.1em;
}
.okk-course .case-schedule__list-dot li::before {
  position: absolute;
  top: 0;
  left: 0;
  content: "・";
}
.okk-course .case-schedule__list-dot li:nth-child(n+2) {
  margin-top: 0.5rem;
}
.okk-course .case-schedule__list-dot li b {
  margin-right: 0.5em;
}
.okk-course__sttl {
  font-size: 2.6rem;
  line-height: 1.4230769231;
  font-weight: 700;
  margin-bottom: 1.4rem;
}
.okk-course__btn {
  margin-top: 6.6rem;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
@media screen and (max-width: 767px) {
  .okk-course {
    margin-bottom: 3rem;
  }
  .okk-course__item:nth-child(n+2) {
    margin-top: 1rem;
  }
  .okk-course__heading {
    padding: 1.4rem 6.6rem 1.4rem 2rem;
    font-size: 1.6rem;
  }
  .okk-course__heading::before {
    right: 1.4rem;
  }
  .okk-course__heading .txt {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 0.6rem;
  }
  .okk-course__heading .txt::before, .okk-course__heading .txt::after {
    right: 2.5rem;
  }
  .okk-course__heading .txt .case {
    font-size: 1.4rem;
  }
  .okk-course__body {
    padding: 2rem 2rem 2.6rem;
  }
  .okk-course .case-schedule {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 1rem;
  }
  .okk-course .case-schedule__time {
    width: 100%;
    font-size: 1.4rem;
  }
  .okk-course .case-schedule__title {
    font-size: 1.6rem;
  }
  .okk-course .case-schedule__image-text {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 1.6rem;
  }
  .okk-course .case-schedule__image {
    width: 29.2rem;
    border-radius: 2rem;
  }
  .okk-course__sttl {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  .okk-course__btn {
    margin-top: 3.5rem;
  }
}

.okk-link-seo {
  padding-bottom: 8.6rem;
}
.okk-link-seo__content {
  max-width: 94.5rem;
  margin: 0 auto;
  background: #f8f8f8;
  border-radius: 2rem;
  padding: 4rem;
}
.okk-link-seo__title {
  font-size: 1.8rem;
  line-height: 1.4444444444;
  font-weight: 700;
  margin-bottom: 1.5rem;
}
.okk-link-seo__title:nth-of-type(n+2) {
  margin-top: 3rem;
}
.okk-link-seo__list {
  max-width: 80rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 0.5rem 3em;
  font-size: 1.4rem;
  line-height: 1.8571428571;
  font-weight: 500;
  overflow: hidden;
}
.okk-link-seo__list li {
  position: relative;
}
.okk-link-seo__list li::before {
  position: absolute;
  top: 50%;
  left: -1.5em;
  content: "｜";
  z-index: 1;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.okk-link-seo a {
  -webkit-transition: 0.25s;
  transition: 0.25s;
  text-decoration: underline;
  text-underline-offset: 2px;
}
@media (hover: hover) {
  .okk-link-seo a:hover {
    text-decoration: none;
  }
}
@media screen and (max-width: 767px) {
  .okk-link-seo {
    padding-top: 3rem;
    padding-bottom: 5rem;
  }
  .okk-link-seo__content {
    border-radius: 1.2rem;
    padding: 2rem;
    gap: 2rem;
  }
  .okk-link-seo__title {
    font-size: 1.6rem;
    margin-bottom: 1.2rem;
  }
  .okk-link-seo__list {
    gap: 0.5rem 3em;
  }
}
@media screen and (max-width: 540px) {
  .okk-link-seo__list {
    font-size: 1.2rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

@media screen and (min-width: 768px) and (max-width: 1260px) {
  .okk-t-links {
    padding-bottom: 2rem;
  }
  .okk-t-rentalplan__item {
    padding: 1rem 0.6rem 0.6rem 1.9rem;
  }
  .okk-ls-services {
    padding: 2rem;
  }
  .okk-ls-services__item__inner {
    padding-right: 1.5rem;
  }
  .okk-ls-services__desc {
    padding-left: 1.5rem;
  }
  .okk-slider-gallery__photo .slider-item {
    width: 30rem;
  }
  .okk-t-returnplan__desc,
  .okk-t-hairset__desc {
    padding: 2rem;
  }
  .okk-t-returnplan__row,
  .okk-t-hairset__row {
    gap: 2rem;
  }
  .okk-t-corporate__content {
    padding: 2rem;
  }
  .okk-t-corporate__item {
    max-width: calc(50% - 1rem);
  }
  .okk-t-column__list,
  .okk-t-publication__list,
  .okk-t-corporate__list,
  .okk-footer-fc {
    gap: 2rem;
  }
}
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .okk-header-top__row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .okk-header__site {
    padding-top: 1rem;
    padding-right: 0;
    padding-bottom: 0;
  }
  .okk-header__group-btn {
    padding-top: 1rem;
    padding-left: calc(100% - 24.7rem);
  }
  .okk-header .okk-box-lang {
    top: 1.2rem;
  }
  .okk-header-main__row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    gap: 2rem;
  }
  .okk-header-nav {
    max-width: 100%;
  }
  .okk-header-nav__col {
    padding: 0 1.5%;
  }
  .okk-header-nav__col dl {
    max-width: 100%;
  }
  .okk-header-nav-pc {
    display: none;
  }
  .okk-btn-wrap {
    gap: 1.4rem;
  }
  .okk-t-kv__title {
    font-size: 3.6rem;
  }
  .okk-t-kv__slogan {
    bottom: 4rem;
  }
  .okk-t-kv .slide-item {
    aspect-ratio: 1366/1100;
  }
  .okk-t-links {
    margin-top: 0;
    padding-bottom: 0;
  }
  .okk-video-popup__content {
    padding: 0.5rem;
    border-radius: 1.2rem;
  }
  .okk-video-popup .close-btn {
    right: 0;
    top: -4rem;
    color: #fff;
  }
  .okk-t-rentalplan__item {
    width: 32rem;
  }
  .okk-t-rentalplan__link .rentalplan-ttl {
    font-size: 3rem;
  }
  .okk-t-hairset__item {
    width: calc(50% - 1rem);
  }
  .okk-t-returnplan__item {
    width: calc(50% - 1rem);
  }
  .okk-t-about__img li {
    width: 13.2rem;
  }
  .okk-t-about__img li span {
    background-size: 100% 62.65rem;
  }
  .okk-t-about__content {
    padding: 4rem 1.5rem;
  }
  .okk-about-thoughts__img li {
    width: 13.2rem;
  }
  .okk-about-thoughts__img li span {
    background-size: 100% 62.7rem;
  }
  .okk-about-thoughts__content {
    padding: 4rem 1.5rem;
  }
  .okk-ls-services__item {
    width: calc(50% - 1rem);
  }
  .okk-t-news .news-time {
    width: 13rem;
  }
  .okk-footer-language li {
    padding: 0 0.7rem;
  }
  .okk-map-modal {
    width: 60rem;
    padding: 2.5rem;
    border-radius: 1.5rem;
  }
  .okk-map-modal__ttl {
    font-size: 3.2rem;
    line-height: 1.40625;
    margin-bottom: 1.5rem;
  }
  .okk-map-modal__photo {
    border-radius: 2rem;
  }
  .okk-map-modal__btn {
    margin-top: 2rem;
  }
  .okk-map-modal__btn a {
    width: 30rem;
  }
  .okk-map-modal__copy {
    margin-top: 1.5rem;
    font-size: 14px;
  }
  .okk-map-modal__slider .slick-arrow {
    width: 5.6rem;
    height: 5.6rem;
    border-width: 1px;
    margin-top: 3rem;
  }
  .okk-map-modal__slider .slick-arrow.slick-prev {
    left: -9rem;
  }
  .okk-map-modal__slider .slick-arrow.slick-next {
    right: -9rem;
  }
  .okk-map-modal .slider-counter {
    bottom: -4.2rem;
    font-size: 1.8rem;
  }
  .okk-map-modal .mfp-close {
    top: -2.5rem;
    right: -6rem;
  }
}
.okk-reserve-store {
  padding: 6rem 0 9rem;
}
.okk-reserve-store__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 3rem;
}
.okk-reserve-store__item {
  width: calc((100% - 9rem) / 4);
  -webkit-box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
          box-shadow: 0 0 0.6rem rgba(0, 0, 0, 0.16);
  border-radius: 1.2rem;
  background: #fff;
  overflow: hidden;
}
@media screen and (min-width: 768px) {
  .okk-reserve-store__item {
    min-width: 26.6rem;
  }
}
.okk-reserve-store__btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  background: #fff;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.okk-reserve-store__img {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  aspect-ratio: 266/160;
  position: relative;
}
.okk-reserve-store__img::after {
  position: absolute;
  content: "";
  z-index: 1;
  width: 4rem;
  height: 4rem;
  bottom: 1rem;
  right: 1rem;
  background: #fff url("../img/common/ico_arrow_right.png") no-repeat center center/1.208rem 0.843rem;
  border: 1px solid #413e3e;
  border-radius: 50%;
}
.okk-reserve-store__ttl {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 2rem 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  font-size: 1.4rem;
  line-height: 1.7142857143;
  font-weight: 700;
}
.okk-reserve-store__ttl .fz-lg {
  font-size: 2rem;
  line-height: 1.4;
  color: #d98794;
}
@media screen and (max-width: 767px) {
  .okk-reserve-store {
    padding: 3rem 0 4rem;
  }
  .okk-reserve-store__list {
    gap: 1.6rem;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .okk-reserve-store__item {
    width: calc(50% - 0.8rem);
  }
  .okk-reserve-store__img {
    aspect-ratio: 160/80;
  }
  .okk-reserve-store__img::after {
    width: 3.2rem;
    height: 3.2rem;
    bottom: 0.5rem;
    right: 0.8rem;
    background-size: 1rem 0.6978476821rem;
  }
  .okk-reserve-store__ttl {
    padding: 1.2rem 1rem;
    font-size: 1.2rem;
    line-height: 1.5;
  }
  .okk-reserve-store__ttl .fz-lg {
    font-size: 1.6rem;
    line-height: 1.375;
  }
}

.okk-reserve-step {
  position: relative;
  z-index: 1;
}
.okk-reserve-step__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 5rem;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.okk-reserve-step__item {
  position: relative;
}
.okk-reserve-step__item:not(:first-child)::before {
  content: "";
  position: absolute;
  top: 4rem;
  right: 50%;
  width: calc(100% + 3rem);
  height: 3px;
  background: #f8f8f8;
  z-index: -1;
}
.okk-reserve-step__item.is-current::before {
  background: #d98794;
}
.okk-reserve-step__item .ico {
  width: 8rem;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
.okk-reserve-step__item .txt {
  font-size: 1.4rem;
  line-height: 2.2857142857;
  font-weight: 500;
  text-align: center;
  margin-top: 1rem;
}
@media screen and (max-width: 767px) {
  .okk-reserve-step__list {
    gap: 1.6rem;
  }
  .okk-reserve-step__item:not(:first-child)::before {
    top: 2.3rem;
  }
  .okk-reserve-step__item .ico {
    width: 4.6rem;
  }
  .okk-reserve-step__item .txt {
    font-size: 1rem;
    line-height: 1.3;
    margin-top: 0.4rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    min-height: 2.8rem;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.okk-reserve-unit .unit {
  padding: 6rem 4rem;
  background-color: #fff;
  border-radius: 1.2rem;
}
.okk-reserve-unit .unit_select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 1rem;
}
.okk-reserve-unit .filter_select_item {
  position: relative;
  width: 14rem;
  font-size: 2rem;
  line-height: 1.5;
}
.okk-reserve-unit .filter_select .select-selected {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  border: 1px solid #707070;
  border-radius: 0.8rem;
  min-height: 5.8rem;
}
.okk-reserve-unit .filter_select .select-selected::after {
  width: 1rem;
  height: 0.5rem;
  background: url(../img/common/ico_arrow01.svg) no-repeat center center;
  background-size: 100% 100%;
}
.okk-reserve-unit .filter_select .select-selected.select-arrow-active::after {
  -webkit-transform: scale(-1);
          transform: scale(-1);
}
.okk-reserve-unit .filter_select .select-items {
  position: absolute;
  background-color: #fff;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 99;
  border: 1px solid #707070;
  border-radius: 0.8rem;
}
.okk-reserve-unit .sel-visittime_note {
  font-size: 1.6rem;
  line-height: 2;
  margin-top: 2rem;
}
.okk-reserve-unit .sel-visittime_note a {
  color: #d98794;
  text-decoration: underline;
}
.okk-reserve-unit .cm-month {
  position: relative;
  width: 100%;
}
.okk-reserve-unit .cm-month .item {
  width: 52.8rem;
  margin-right: 2rem;
  background-color: #fff;
}
.okk-reserve-unit .cm-month .item:last-child {
  margin-right: 0;
}
.okk-reserve-unit .cm-month .item_month {
  color: #d98794;
  font-size: 2.4rem;
  line-height: 1.7916666667;
  font-weight: bold;
  text-align: center;
  margin-bottom: 1.6rem;
}
.okk-reserve-unit .cm-month .item .cm-tbl {
  width: 100%;
  border-collapse: collapse;
  font-size: 1.6rem;
  color: #000;
  line-height: 1.875;
  text-align: center;
  font-weight: bold;
  background-color: #f8f8f8;
}
.okk-reserve-unit .cm-month .item .cm-tbl td {
  border: 1px solid #E2E2E2;
}
.okk-reserve-unit .cm-month .item .cm-tbl th {
  padding: 0.1rem 0;
  text-align: center;
  font-weight: bold;
  width: 14.2857143%;
  background-color: #d98794;
  color: #fff;
  font-size: 1.6rem;
  line-height: 2;
}
.okk-reserve-unit .cm-month .item .cm-tbl tbody tr:last-child td {
  border-bottom: none;
}
.okk-reserve-unit .cm-month .item .cm-tbl td {
  width: 14.2857143%;
  font-size: 1.6rem;
  line-height: 2;
}
.okk-reserve-unit .cm-month .item .cm-tbl td:first-child {
  border-left: none;
}
.okk-reserve-unit .cm-month .item .cm-tbl td:last-child {
  border-right: none;
}
.okk-reserve-unit .cm-month .item .cm-tbl td a {
  padding: 1rem 0;
  color: inherit;
  display: block;
  outline: none;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.okk-reserve-unit .cm-month .item .cm-tbl td a:hover {
  background-color: #f7c5c5;
}
.okk-reserve-unit .cm-month .item .cm-tbl td a.day_active {
  color: #413e3e;
  background-color: #FFFABF;
}
.okk-reserve-unit .cm-month .item .cm-tbl td.c-sun {
  color: #d98794;
  background-color: #faf2f4;
}
.okk-reserve-unit .cm-month .item .cm-tbl td.c-sat {
  color: #90b5d1;
  background-color: #f2f5f7;
}
.okk-reserve-unit .cm-month .item .cm-tbl td.off a {
  opacity: 0;
}
.okk-reserve-unit .cm-month .item .cm-tbl .c-disable {
  color: #ccc !important;
  background-color: #efefef !important;
}
.okk-reserve-unit .cm-month .item .cm-tbl .c-disable a {
  pointer-events: none;
  cursor: default;
}
.okk-reserve-unit .cm-month .slick-prev,
.okk-reserve-unit .cm-month .slick-next {
  width: 4.5rem;
  height: 4.5rem;
  background: url(../img/common/ico_arrow02.png) no-repeat center center #fff;
  background-size: 4.5rem;
  top: 0;
  z-index: 10;
  border-radius: 50%;
}
.okk-reserve-unit .cm-month .slick-prev::before,
.okk-reserve-unit .cm-month .slick-next::before {
  content: none;
}
.okk-reserve-unit .cm-month .slick-prev {
  background-image: url(../img/common/ico_arrow02_left.png);
  right: 5rem;
  left: auto;
}
.okk-reserve-unit .cm-month .slick-next {
  background-image: url(../img/common/ico_arrow02.png);
  right: 0;
}
.okk-reserve-unit .cm-month .slick-disabled {
  visibility: hidden;
}
.okk-reserve-unit .sel-visittime {
  margin-top: 5.6rem;
}
.okk-reserve-unit .sel-visittime_note {
  font-size: 1.6rem;
  line-height: 2;
  font-weight: 500;
  margin-bottom: 2.5rem;
  text-align: left;
}
.okk-reserve-unit .sel-visittime .item_month {
  background-color: #d98794;
  color: #fff;
  font-weight: bold;
  font-size: 2.4rem;
  line-height: 1.7916666667;
  text-align: center;
  padding: 0.4rem 1rem;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl {
  width: 100%;
  border-collapse: collapse;
  font-size: 1.6rem;
  line-height: 2;
  color: #bc0e1d;
  text-align: center;
  font-weight: bold;
  background-color: #fff;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl th,
.okk-reserve-unit .sel-visittime .item .cm-tbl td {
  border: 1px solid #e6e6e6;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl th {
  padding: 0.3rem 0;
  text-align: center;
  font-weight: bold;
  color: #000;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl th.c-sun {
  color: #d98794;
  background-color: #faf2f4;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl th.c-sat {
  color: #90b5d1;
  background-color: #f2f5f7;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl td {
  padding: 0.3rem 0;
  font-family: "Yu Gothic", YuGothic, "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "メイリオ", Meiryo, "MS Pゴシック", "MS PGothic", sans-serif;
  cursor: pointer;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl td.wait_cancel {
  background-color: #f8f8f8;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl td.wait_cancel a {
  color: inherit;
  border-bottom: 1px solid #d98794;
  outline: none;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl td.time_check {
  background-color: #FDFABF;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl .c-time {
  width: 7.5rem;
  padding: 1rem 1.5rem;
}
.okk-reserve-unit .sel-visittime .item .cm-tbl .space {
  width: 0;
  border: none;
  padding: 0;
  display: none;
}
.okk-reserve-unit .sel-visittime .slick-prev,
.okk-reserve-unit .sel-visittime .slick-next {
  width: 4.5rem;
  height: 4.5rem;
  background: url(../img/common/ico_arrow02.png) no-repeat center center #fff;
  background-size: 4.5rem;
  top: -3.5rem;
  z-index: 10;
  border-radius: 50%;
}
.okk-reserve-unit .sel-visittime .slick-prev::before,
.okk-reserve-unit .sel-visittime .slick-next::before {
  content: none;
}
.okk-reserve-unit .sel-visittime .slick-prev {
  background-image: url(../img/common/ico_arrow02_left.png);
  right: 5rem;
  left: auto;
}
.okk-reserve-unit .sel-visittime .slick-next {
  background-image: url(../img/common/ico_arrow02.png);
  right: 0;
}
.okk-reserve-unit .sel-visittime .slick-disabled {
  visibility: hidden;
}
@media screen and (max-width: 767px) {
  .okk-reserve-unit .sel-visittime {
    margin-top: 5.6rem;
  }
  .okk-reserve-unit .sel-visittime_note {
    font-size: 1.4rem;
    line-height: 1.875;
    color: #202020;
    margin-bottom: 2rem;
  }
  .okk-reserve-unit .sel-visittime .item {
    overflow: hidden;
  }
  .okk-reserve-unit .sel-visittime .item_inner {
    overflow-x: scroll;
    padding-bottom: 1rem;
  }
  .okk-reserve-unit .sel-visittime .item_inner::-webkit-scrollbar {
    height: 0.8rem;
  }
  .okk-reserve-unit .sel-visittime .item_inner::-webkit-scrollbar-track {
    background: #ececec;
  }
  .okk-reserve-unit .sel-visittime .item_inner::-webkit-scrollbar-thumb {
    background: #d98794;
  }
  .okk-reserve-unit .sel-visittime .item_month {
    background-color: #d98794;
    color: #fff;
    font-weight: bold;
    font-size: 1.4rem;
    text-align: center;
    line-height: 1.875;
    padding: 0.5rem 1rem;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl {
    width: 100%;
    border-collapse: collapse;
    font-size: 1.4rem;
    color: #d98794;
    line-height: 1.875;
    text-align: center;
    font-weight: bold;
    background-color: #fff;
    min-width: 65rem;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl th,
  .okk-reserve-unit .sel-visittime .item .cm-tbl td {
    border: 1px solid #e6e6e6;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl th {
    padding: 0.6rem 0.2rem;
    text-align: center;
    font-weight: bold;
    color: #000;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl th.c-sun {
    color: #d98794;
    background-color: #fff5f6;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl th.c-sat {
    color: #0f50fb;
    background-color: #ecf8ff;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl td {
    padding: 0.6rem 0;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl td.wait_cancel {
    background-color: #efefef;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl td.wait_cancel a {
    color: inherit;
    border-bottom: 1px solid #c96469;
    outline: none;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl td.time_check {
    background-color: #d98794;
    color: #fff;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl tr {
    position: relative;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl .c-time {
    width: 7.5rem;
    padding: 0.6rem 1.5rem;
    position: sticky;
    top: 0;
    left: 0;
    background: #fff;
    z-index: 10;
  }
  .okk-reserve-unit .sel-visittime .item .cm-tbl .space {
    width: 7.3rem;
    padding: 0.6rem 1.5rem;
  }
}
.okk-reserve-unit .reserveIndexForm-back {
  margin-top: 5rem;
  text-align: center;
}
.okk-reserve-unit .reserveIndexForm-back a {
  color: #413e3e;
  font-weight: bold;
  font-size: 1.6rem;
  line-height: 2;
  text-decoration: underline;
  padding-left: 2rem;
  position: relative;
  text-underline-offset: 2px;
}
.okk-reserve-unit .reserveIndexForm-back a::after {
  position: absolute;
  content: "";
  z-index: 1;
  background: url("../img/common/ico_arrow_left.png") no-repeat right center/cover;
  width: 1.2rem;
  height: 0.8rem;
  top: 55%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.okk-reserve-unit .cm-form_ttl {
  font-size: 3.2rem;
  line-height: 1.78125;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #d98794;
}
.okk-reserve-unit .reserveForm {
  padding-bottom: 12rem;
  margin-top: 9rem;
}
.okk-reserve-unit .reserveForm_ttl {
  margin-bottom: 2.5rem;
  text-align: center;
}
.okk-reserve-unit .reserveForm-copy {
  font-size: 1.6rem;
  line-height: 1.875;
  text-align: center;
  margin-bottom: 4rem;
}
.okk-reserve-unit .reserveForm-cpl {
  font-size: 2.2rem;
  line-height: 1.875;
  margin-bottom: 4rem;
  text-align: center;
}
.okk-reserve-unit .reserveForm-form_item {
  margin-bottom: 4rem;
  font-size: 1.6rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.okk-reserve-unit .reserveForm-form_item .form-lbl {
  width: 20.7612456747%;
  padding-right: 1.5rem;
  padding-top: 1.3rem;
  font-weight: bold;
  text-align: left;
}
.okk-reserve-unit .reserveForm-form_item .form-lbl p {
  color: #413e3e;
}
.okk-reserve-unit .reserveForm-form_item .form-lbl p .c-red {
  background: #d98794;
  color: #fff !important;
  border-radius: 2px;
  font-size: 1.2rem;
  line-height: 2;
  letter-spacing: 0.01em;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 4rem;
  margin-right: 0.6rem;
}
.okk-reserve-unit .reserveForm-form_item .form-lbl p .notice {
  color: #9d9d9d;
  font-weight: normal;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt {
  display: block;
  width: 77.8546712803%;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input,
.okk-reserve-unit .reserveForm-form_item .form-cnt textarea {
  width: 100%;
  padding: 1.7rem 2rem;
  border: 1px solid #707070;
  outline: none;
  font-size: 1.6rem;
  line-height: 2;
  font-weight: 500;
  border-radius: 8px;
  background-color: transparent;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input::-webkit-input-placeholder, .okk-reserve-unit .reserveForm-form_item .form-cnt textarea::-webkit-input-placeholder {
  color: #B1B1B1;
  font-size: inherit;
  font-weight: inherit;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input::-moz-placeholder, .okk-reserve-unit .reserveForm-form_item .form-cnt textarea::-moz-placeholder {
  color: #B1B1B1;
  font-size: inherit;
  font-weight: inherit;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input:-ms-input-placeholder, .okk-reserve-unit .reserveForm-form_item .form-cnt textarea:-ms-input-placeholder {
  color: #B1B1B1;
  font-size: inherit;
  font-weight: inherit;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input::-ms-input-placeholder, .okk-reserve-unit .reserveForm-form_item .form-cnt textarea::-ms-input-placeholder {
  color: #B1B1B1;
  font-size: inherit;
  font-weight: inherit;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt input::placeholder,
.okk-reserve-unit .reserveForm-form_item .form-cnt textarea::placeholder {
  color: #B1B1B1;
  font-size: inherit;
  font-weight: inherit;
}
.okk-reserve-unit .reserveForm-form_item .form-cnt .notice {
  font-size: 1.2rem;
  line-height: 2;
  margin-top: 1rem;
  text-align: left;
}
.okk-reserve-unit .reserveForm-form_information {
  background-color: #fff;
  padding: 1rem 2rem 2rem;
  color: #202020;
  margin-bottom: 4rem;
}
.okk-reserve-unit .reserveForm-form_information .ttl {
  margin-bottom: 1rem;
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: bold;
}
.okk-reserve-unit .reserveForm-form_information .copy {
  font-size: 1.4rem;
  font-weight: 300;
}
.okk-reserve-unit .reserveForm-form_information .copy a {
  color: #d98794;
  border-bottom: 1px solid #d98794;
}
.okk-reserve-unit .reserveForm [class*=mdl-btn-] {
  margin: 0 2.8rem;
  color: #fff;
  background-color: #d98794;
  -webkit-box-shadow: none;
          box-shadow: none;
}
@media print, screen and (max-width: 1040px) and (min-width: 900px) {
  .okk-reserve-unit .cm-month {
    margin-left: -1rem;
    margin-right: -1rem;
  }
  .okk-reserve-unit .cm-month .item {
    width: calc(50vw - 3rem);
    margin: 0 1rem;
  }
  .okk-reserve-unit .cm-month .slick-prev {
    right: 6rem;
    left: auto;
  }
  .okk-reserve-unit .cm-month .slick-next {
    right: 1rem;
  }
}
.okk-reserve-unit #form-modal {
  width: 115.6rem;
  max-width: calc(100vw - 4rem);
  background-color: #fff;
  padding: 4rem 0;
  margin: 0 auto;
  -webkit-box-shadow: 0.7rem 0.7rem 3rem 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0.7rem 0.7rem 3rem 0 rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: -1;
  opacity: 0;
  -webkit-transition: z-index 0s, opacity 0.5s, -webkit-transform 0.3s;
  transition: z-index 0s, opacity 0.5s, -webkit-transform 0.3s;
  transition: z-index 0s, opacity 0.5s, transform 0.3s;
  transition: z-index 0s, opacity 0.5s, transform 0.3s, -webkit-transform 0.3s;
  -webkit-transform: translate(-50%, 0%);
          transform: translate(-50%, 0%);
  height: 90vh;
  border-radius: 12px;
}
.okk-reserve-unit #form-modal.visible {
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  opacity: 1;
  z-index: 2001;
}
.okk-reserve-unit #form-modal .form-modal_inner {
  padding: 0 4rem;
  overflow-y: scroll;
  overflow-x: hidden;
  height: 100%;
}
.okk-reserve-unit #form-modal .form-modal_inner::-webkit-scrollbar {
  width: 0.3rem;
}
.okk-reserve-unit #form-modal .form-modal_inner::-webkit-scrollbar-track {
  background: #ccc;
}
.okk-reserve-unit #form-modal .form-modal_inner::-webkit-scrollbar-thumb {
  background: #d98794;
  cursor: pointer;
}
.okk-reserve-unit #form-modal .closeBtn {
  position: absolute;
  top: 2rem;
  right: 2rem;
  cursor: pointer;
}
.okk-reserve-unit #form-modal .reserveForm {
  margin-top: 1rem;
  padding-bottom: 2rem;
}
/*# sourceMappingURL=common.css.map */